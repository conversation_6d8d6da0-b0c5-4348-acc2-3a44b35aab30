import { createAppAsyncThunk } from "@/common/vendors/redux/store/createAppAsyncThunk";

import mobileProfileRequest from "../api/mobile-profile.api";
import { MobileProfileQuery } from "../interface/mobile-profile.query";

export const getMobileProfile = createAppAsyncThunk(
  "mobileProfile/list",
  async (query: MobileProfileQuery) => {
    const { page = 1, pageSize = 10, ...otherQueries } = query;
    const response = await mobileProfileRequest.getList({
      skipCount: (page - 1) * pageSize,
      maxResultCount: pageSize,
      ...otherQueries,
      isActive:
        otherQueries?.isActive === "true"
          ? true
          : otherQueries?.isActive === "false"
            ? false
            : undefined,
    });
    return response;
  },
);
