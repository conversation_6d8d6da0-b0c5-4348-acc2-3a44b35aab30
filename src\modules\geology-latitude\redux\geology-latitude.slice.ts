import { createSlice } from "@reduxjs/toolkit";

import { RequestState } from "@/common/configs/app.contants";

import { getDetailGeologyLatitude, getListGeologyLatitude } from "./thunks";

const initialState: GeologyLatitudeSliceState = {
  status: RequestState.idle,
  getDetailStatus: RequestState.idle,
};

export const geologyLatitudeSlice = createSlice({
  name: "geologyLatitude",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getListGeologyLatitude.pending, (state) => {
        state.status = RequestState.pending;
      })
      .addCase(getListGeologyLatitude.fulfilled, (state, action) => {
        state.status = RequestState.success;
        state.result = action.payload.data;
      })
      .addCase(getDetailGeologyLatitude.pending, (state) => {
        state.getDetailStatus = RequestState.pending;
      })
      .addCase(getDetailGeologyLatitude.fulfilled, (state, action) => {
        state.getDetailStatus = RequestState.success;
        state.detail = action.payload;
      });
  },
});

export interface GeologyLatitudeSliceState {
  result?: any;
  status: RequestState;
  getDetailStatus: RequestState;
  detail?: any;
}

export const {} = geologyLatitudeSlice.actions;
