import { injectable } from "inversify";
import Cookies from "js-cookie";

import IStorage from "@/common/interfaces/storage/IStorage";

@injectable()
export class CookiesStorage implements IStorage {
  setItem(key: string, value: string, options: Cookies.CookieAttributes = {}) {
    return Cookies.set(key, value, {
      sameSite: "strict",
      secure: true,
      ...options,
    });
  }
  getItem(key: string) {
    return Cookies.get(key);
  }
  removeItem(key: string) {
    Cookies.remove(key);
    return key;
  }
}
