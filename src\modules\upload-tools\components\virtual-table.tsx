import { Table, type TableProps } from "antd";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { AutoSizer, MultiGrid } from "react-virtualized";

const AutoSizerComponent: any = AutoSizer;
const MultiGridComponent: any = MultiGrid;

interface VirtualTableProps<T> extends TableProps<T> {
  isEditMode?: boolean;
  formState?: {
    errors?: Record<string, any>;
  };
}

const VirtualTable = <T extends object>(props: VirtualTableProps<T>) => {
  const { columns, scroll, dataSource, isEditMode } = props;
  const [tableWidth] = useState(0);
  const gridRef = useRef<any>();

  const widthColumnCount = columns!.filter(({ width }) => !width).length;
  const mergedColumns = useMemo(() => {
    return columns!.map((column) => {
      if (column.width) {
        return column;
      }
      return {
        ...column,
        width: Math.floor(tableWidth / widthColumnCount),
      };
    });
  }, [columns, tableWidth, widthColumnCount]);

  const headerRenderer = useCallback(
    ({ columnIndex, key, style }: any) => {
      const col = mergedColumns[columnIndex];
      return (
        <div
          key={key}
          style={{
            ...style,
            padding: "8px",
            borderBottom: "2px solid #aaa",
            backgroundColor: "#fafafa",
            fontWeight: "bold",
            display: "flex",
            alignItems: "center",
          }}
        >
          {typeof col.title === "function" ? col.title({}) : col.title}
        </div>
      );
    },
    [mergedColumns],
  );

  const cellRenderer = useCallback(
    ({ columnIndex, rowIndex, _, style }: any) => {
      const record = dataSource?.[rowIndex];
      if (!record) {
        return null;
      }

      const col = mergedColumns[columnIndex];
      const value = record[(col as any).dataIndex];
      const cellKey = `${rowIndex}-${columnIndex}-${(col as any).dataIndex}-${
        props.isEditMode
      }`;

      // Force validation for all cells
      const error =
        props.formState?.errors?.[
          `dataUpload.${rowIndex}.${(col as any).dataIndex}`
        ];
      const hasError = !!error;

      return (
        <div
          key={cellKey}
          style={{
            ...style,
            padding: "8px",
            borderBottom: "1px solid #f0f0f0",
            display: "flex",
            alignItems: "center",
            backgroundColor: hasError ? "#fff2f0" : undefined,
          }}
        >
          {col.render ? col.render(value, record, rowIndex) : value}
          {hasError && (
            <div
              style={{ color: "#ff4d4f", marginLeft: "8px", fontSize: "12px" }}
            >
              {error?.message}
            </div>
          )}
        </div>
      );
    },
    [dataSource, mergedColumns, props.isEditMode, props.formState?.errors],
  );

  // Add effect to validate all inputs when form state changes
  useEffect(() => {
    if (gridRef.current) {
      gridRef.current.recomputeGridSize();
      gridRef.current.forceUpdate();
    }
  }, [props.formState?.errors]);

  const renderVirtualList = useCallback(() => {
    return (
      <div style={{ height: scroll!.y }}>
        <AutoSizerComponent>
          {({ width, height }) => (
            <MultiGridComponent
              ref={gridRef}
              className="virtual-table-grid"
              columnCount={mergedColumns.length}
              columnWidth={({ index }) => mergedColumns[index].width as number}
              height={height}
              rowCount={(dataSource?.length ?? 0) + 1} // +1 for header row
              rowHeight={({ index }) => (index === 0 ? 50 : 54)} // Header row is shorter
              width={width}
              cellRenderer={({ columnIndex, rowIndex, key, style }) => {
                if (rowIndex === 0) {
                  return headerRenderer({ columnIndex, key, style });
                }
                return cellRenderer({
                  columnIndex,
                  rowIndex: rowIndex - 1,
                  key,
                  style,
                });
              }}
              hideTopRightGridScrollbar
              hideBottomLeftGridScrollbar
              enableFixedColumnScroll
              enableFixedRowScroll
              styleBottomLeftGrid={{
                borderRight: "2px solid #aaa",
              }}
              styleTopLeftGrid={{
                borderBottom: "2px solid #aaa",
                borderRight: "2px solid #aaa",
                backgroundColor: "#fafafa",
              }}
              styleTopRightGrid={{
                borderBottom: "2px solid #aaa",
                backgroundColor: "#fafafa",
              }}
              key={`grid-${isEditMode}-${JSON.stringify(
                props.formState?.errors,
              )}`}
              overscanRowCount={50} // Increase overscan to validate more rows
              overscanColumnCount={5}
            />
          )}
        </AutoSizerComponent>
      </div>
    );
  }, [
    cellRenderer,
    headerRenderer,
    mergedColumns,
    scroll,
    dataSource,
    isEditMode,
    props.formState?.errors,
  ]);

  // Add effect to force re-render when isEditMode changes
  useEffect(() => {
    if (gridRef.current) {
      gridRef.current.recomputeGridSize();
      gridRef.current.forceUpdate();
    }
  }, [isEditMode]);

  const memoizedComponents = useMemo(
    () => ({
      body: renderVirtualList as any,
    }),
    [renderVirtualList],
  );

  return (
    <Table
      {...props}
      columns={mergedColumns}
      dataSource={dataSource}
      pagination={false}
      components={{
        ...memoizedComponents,
        header: {
          wrapper: () => null,
        },
      }}
    />
  );
};

export default React.memo(VirtualTable);
