/* eslint-disable react-hooks/exhaustive-deps */
import { zodResolver } from "@hookform/resolvers/zod";
import { Form } from "antd";
import { useForm } from "react-hook-form";
import { toast } from "react-toastify";

import { ModalType } from "@/common/configs/app.enum";
import { ButtonCommon } from "@/components/common/button-common";
import { InputTextCommon } from "@/components/common/input-text";
import { ModalCommon } from "@/components/common/modal-common";
import { ToogleCommon } from "@/components/common/toogle-common";

import { useCreateGeologyCheckbox } from "../hooks/useCreateGeologyCheckbox";
import { useDeleteGeologyCheckbox } from "../hooks/useDeleteGeologyCheckbox";
import { useUpdateGeologyCheckbox } from "../hooks/useUpdateGeologyCheckbox";
import {
  GeologyCheckboxBody,
  GeologyCheckboxBodyType,
} from "../model/schema/geology-checkbox.schema";

export interface IModalGeologyCheckboxProps {
  modalState: {
    isOpen: boolean;
    detailInfo: any;
    type: string;
  };
  setModalState: (value: any) => void;
  refresh: () => void;
}

export function ModalGeologyCheckbox(props: IModalGeologyCheckboxProps) {
  const { modalState, setModalState, refresh } = props;
  const {
    request: requestCreateGeologyCheckbox,
    loading: loadingCreateGeologyCheckbox,
  } = useCreateGeologyCheckbox();
  const {
    request: requestUpdateGeologyCheckbox,
    loading: loadingUpdateGeologyCheckbox,
  } = useUpdateGeologyCheckbox();
  const {
    request: requestDeleteGeologyCheckbox,
    loading: loadingDeleteGeologyCheckbox,
  } = useDeleteGeologyCheckbox();
  const { control, handleSubmit } = useForm<GeologyCheckboxBodyType>({
    resolver: zodResolver(GeologyCheckboxBody),
    defaultValues: {
      ...modalState?.detailInfo,
      isActive: modalState?.detailInfo?.isActive ?? true,
    },
  });
  const handleCancel = () => {
    setModalState({ ...modalState, isOpen: false });
  };
  const isConfirm = modalState.type === ModalType.DELETE;
  const onSubmit = (values: GeologyCheckboxBodyType) => {
    if (modalState.type === ModalType.CREATE) {
      requestCreateGeologyCheckbox(values, () => {
        toast.success("Create geology checkbox successfully");
        setModalState({ ...modalState, isOpen: false });
        refresh();
      });
    }
    if (modalState.type === ModalType.UPDATE) {
      requestUpdateGeologyCheckbox(
        {
          ...values,
          id: modalState.detailInfo.id,
        },
        () => {
          setModalState({ ...modalState, isOpen: false });
          toast.success("Update geology checkbox successfully");
          refresh();
        },
      );
    }
  };

  const handleDelete = () => {
    requestDeleteGeologyCheckbox(
      {
        id: modalState.detailInfo.id,
      },
      () => {
        setModalState({ ...modalState, isOpen: false });
        refresh();
      },
      (err) => {
        toast.error(err?.message);
      },
    );
  };

  return (
    <ModalCommon
      open={modalState.isOpen}
      centered
      padding={0}
      footer={null}
      onCancel={handleCancel}
      style={{ borderRadius: 8 }}
      width={450}
      closable={false}
    >
      {isConfirm ? (
        <div className="flex flex-col gap-2">
          <p className="font-bold text-16-18 capitalize font-visby">
            Are you sure you want to delete this geology checkbox?
          </p>
          <p>
            This action cannot be undone. This will permanently delete the
            geology checkbox
          </p>
          <div className="flex justify-end gap-2">
            <ButtonCommon onClick={handleCancel} className="btn btn-sm">
              No
            </ButtonCommon>
            <ButtonCommon
              loading={loadingDeleteGeologyCheckbox}
              onClick={handleDelete}
              className="btn btn-sm bg-primary text-white hover:bg-primary"
            >
              Yes
            </ButtonCommon>
          </div>
        </div>
      ) : (
        <div className="px-6 flex flex-col gap-4">
          <p className="font-bold text-24-28 capitalize text-center font-visby">
            {modalState.type === ModalType.UPDATE
              ? "Update geology checkbox"
              : "Add geology checkbox"}
          </p>
          <Form
            onFinish={handleSubmit(onSubmit)}
            className="flex flex-col gap-3"
          >
            <InputTextCommon
              label="Name"
              name="name"
              placeholder="Type geology checkbox name here"
              control={control}
            />

            <ToogleCommon label="Is Active" name="isActive" control={control} />

            <div className="flex flex-col gap-3 mt-3">
              <ButtonCommon
                type="submit"
                loading={
                  loadingCreateGeologyCheckbox || loadingUpdateGeologyCheckbox
                }
                className="btn btn-sm w-full hover:bg-primary-hover bg-primary text-white border-none"
              >
                {modalState.type === ModalType.UPDATE
                  ? "Update geology checkbox"
                  : "Add geology checkbox"}
              </ButtonCommon>
              <ButtonCommon
                onClick={handleCancel}
                className="btn py-2 w-full btn-sm bg-slate-400 text-white border-none hover:bg-slate-500"
              >
                Cancel
              </ButtonCommon>
            </div>
          </Form>
        </div>
      )}
    </ModalCommon>
  );
}
