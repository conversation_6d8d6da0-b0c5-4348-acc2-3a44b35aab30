import { RequestState } from "@/common/configs/app.contants";
import { appRequest } from "@/common/configs/app.di-container";
import http from "@/lib/http";
import { getErrorMessage } from "@/utils/error.utils";

import { ImageQuery, ImageViewQuery } from "../interface/image.interface";
import { ImageBodyType } from "../model/schema/image.schema";

const imageRequest = {
  getImages: async (params: ImageQuery) => {
    try {
      const response = await appRequest.get<any>("/services/app/Image/GetAll", {
        ...params,
        projectIds: JSON.stringify(params.projectIds),
        prospectIds: JSON.stringify(params.prospectIds),
        holeIds: JSON.stringify(params.holeIds),
        DrillHoleNames: JSON.stringify(params.drillHoleNames),
        ignoreFields: JSON.stringify(params.ignoreFields),
        skipCount: params.skipCount === "null" ? 0 : (params.skipCount ?? 0),
      });

      const skipCount = params.skipCount ?? 0;
      const maxResultCount = params.maxResultCount ?? 10;

      return {
        state: RequestState.success,
        data: {
          items: response?.result?.items,
          pagination: {
            current:
              maxResultCount > 0
                ? Math.floor(skipCount / maxResultCount) + 1
                : 1,
            pageSize: maxResultCount,
            total: response?.result?.totalCount,
          },
        },
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  getListImage: async (params: ImageQuery) => {
    try {
      return http.get(`/services/app/Image/GetAll`, {
        params: {
          ...params,
          projectIds: JSON.stringify(params.projectIds),
          prospectIds: JSON.stringify(params.prospectIds),
          holeIds: JSON.stringify(params.holeIds),
          DrillHoleNames: JSON.stringify(params.drillHoleNames),
          ignoreFields: JSON.stringify(params.ignoreFields),
        },
      });
    } catch (error) {
      return Promise.reject(error);
    }
  },
  getAllByView: async (params: ImageViewQuery) => {
    try {
      return http.get(`/services/app/Image/GetAllByView`, {
        params: {
          ...params,
          drillHoleNames: JSON.stringify(params.drillHoleNames),
          imageFilter: JSON.stringify(params.imageFilter),
        },
      });
    } catch (error) {
      return Promise.reject(error);
    }
  },
  getImage: async (params: { id: number }) => {
    try {
      const response = await appRequest.get<any>(
        `/services/app/Image/Get`,
        params,
      );
      return {
        state: RequestState.success,
        data: {
          items: response?.result?.items,
        },
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  getResultImage: async (id: any) => {
    try {
      const response = await appRequest.get<any>(
        `/services/app/Image/GetResultImage`,
        { params: { id } },
      );
      return {
        state: RequestState.success,
        data: response?.result?.items,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  getImageCropOcr: async (imageId: any) => {
    try {
      const response = await appRequest.get<any>(
        `/services/app/ImageCrop/GetImageCropOcr`,
        { params: { imageId } },
      );
      return {
        state: RequestState.success,
        data: response?.result?.items,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },

  createImage: async (body: {
    id: any;
    cropCoordinate: {
      x: number;
      y: number;
      width: number;
      height: number;
      id?: any;
      type?: any;
    }[];
  }) => {
    try {
      const response = await appRequest.post<any>(
        `/services/app/Image/CropImg`,
        body,
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  alignPolygon: async (body: {
    id: any;
    coordinate?: {
      x: number;
      y: number;
      width: number;
      height: number;
    };
  }) => {
    try {
      const response = await appRequest.post<any>(
        `/services/app/Image/AlignPolygon`,
        body,
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  cropImage: async (body: any) => {
    try {
      const response = await appRequest.post<any>(
        `/services/app/Image/CropImg`,
        body,
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  updateImage: async (body: ImageBodyType) => {
    try {
      const response = await appRequest.put<any>(
        `/services/app/Image/UpdateImage`,
        body,
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },

  uploadImageToProject: async ({
    file,
    drillHoleId,
    projectId,
    prospectId,
    boundingBoxId,
    boundingRowsId,
    workflowId,
    isUnnamed,
    imageCategory,
    imageTypeId,
    imageSubtypeId,
  }: any) => {
    try {
      const response = await appRequest.upload("/services/app/Image/Create", [
        {
          key: "image",
          value: file,
        },
        {
          key: "imageCategory",
          value: imageCategory,
        },
        {
          key: "imageTypeId",
          value: imageTypeId,
        },
        {
          key: "imageSubtypeId",
          value: imageSubtypeId,
        },
        {
          key: "holeId",
          value: drillHoleId,
        },
        {
          key: "projectId",
          value: projectId,
        },
        {
          key: "prospectId",
          value: prospectId,
        },
        {
          key: "boundingBoxId",
          value: boundingBoxId,
        },
        {
          key: "boundingRowsId",
          value: boundingRowsId,
        },
        {
          key: "workflowId",
          value: workflowId,
        },
        {
          key: "isUnnamed",
          value: isUnnamed,
        },
      ]);
      return {
        state: RequestState.success,
        data: response as any,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },

  uploadImage: async ({ image }) => {
    try {
      const response = await appRequest.upload(
        "/services/app/Image/UploadImg",
        [
          {
            key: "image",
            value: image,
          },
        ],
      );
      return {
        state: RequestState.success,
        data: response as any,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },

  deleteImages: async (params: { ids: number[] }) => {
    try {
      const response = await appRequest.delete<any>(
        `/services/app/Image/DeleteMulti?ids=${JSON.stringify(params.ids)}`,
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },

  validateImages: async (body: any) => {
    try {
      const response: any = await appRequest.post(
        `/services/app/Image/ValidateImageName`,
        body,
      );
      return {
        state: RequestState.success,
        data: response?.result ?? [],
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },

  getListCroppedImage: async (params: {
    imageId?: number;
    skipCount?: number;
    maxResultCount?: number;
  }) => {
    try {
      const apiParams: Record<string, any> = {};
      if (params.imageId !== undefined) {
        apiParams.imageId = params.imageId;
      }
      if (params.skipCount !== undefined) {
        apiParams.skipCount = params.skipCount;
      }
      if (params.maxResultCount !== undefined) {
        apiParams.maxResultCount = params.maxResultCount;
      }

      const finalApiParams = Object.fromEntries(
        Object.entries(apiParams).filter(
          ([_, v]) => v !== undefined && v !== null,
        ),
      );

      const response = await appRequest.get<any>(
        `/services/app/ImageCrop/GetAll`,
        finalApiParams,
      );

      const currentSkipCount = params.skipCount ?? 0;
      const currentMaxResultCount = params.maxResultCount ?? 10;

      return {
        state: RequestState.success,
        data: {
          items: response?.result?.items,
          pagination: {
            current:
              currentMaxResultCount > 0
                ? Math.floor(currentSkipCount / currentMaxResultCount) + 1
                : 1,
            pageSize: currentMaxResultCount,
            total: response?.result?.totalCount,
          },
        },
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  processImage: async (body: { workflowId: any; imageId: any }) => {
    try {
      const response = await appRequest.post<any>(
        `/services/app/Image/ProcessImage`,
        body,
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  updateResultOCR: async (body: {
    id: any;
    ocr?: string;
    directOcr?: string;
    segment?: string;
  }) => {
    try {
      const response = await appRequest.put<any>(
        `/services/app/Image/UpdateResultOcrSegment`,
        body,
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  confirmDrillholeOCR: async (body: {
    imageId: any;
    projectId?: string;
    prospectId?: string;
    drillholeName?: string;
    depthFrom?: number;
    depthTo?: number;
    status?: number;
  }) => {
    try {
      const response = await appRequest.post<any>(
        `/services/app/Image/ConfirmDrillholeOcr`,
        body,
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  getSkipCountByDepth: async (params: ImageQuery) => {
    try {
      const response = await appRequest.get<any>(
        `/services/app/Image/GetSkipCountByDepth`,
        params,
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
};

export default imageRequest;
