import { Select } from "antd";
import React, { memo, useCallback, useState } from "react";
import { Control, Controller } from "react-hook-form";

import { ErrorTooltip } from "./error-tooltip";

interface ColourOption {
  id: number;
  name: string;
  hexCode: string;
}

interface FieldColourProps {
  control: Control<any>;
  name: string;
  disabled?: boolean;
  mandatory?: boolean;
  options?: ColourOption[];
  onKeyDown?: (event: React.KeyboardEvent) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  className?: string;
  id?: string;
  onFieldChange?: (rowIndex: number, fieldPath: string, value: any) => void;
  rowIndex?: number;
  fieldPath?: string;
}

export const FieldColour = memo<FieldColourProps>(
  ({
    control,
    name,
    disabled = false,
    mandatory = false,
    options = [],
    onKeyDown,
    onFocus,
    onBlur,
    className,
    id,
    onFieldChange,
    rowIndex,
    fieldPath,
  }) => {
    const [dropdownOpen, setDropdownOpen] = useState(false);
    const handleKeyDown = useCallback(
      (event: React.KeyboardEvent) => {
        const arrowKeys = [
          "ArrowUp",
          "ArrowDown",
          "ArrowLeft",
          "ArrowRight",
          "Tab",
        ];

        if (arrowKeys.includes(event.key)) {
          if (event.shiftKey) {
            setDropdownOpen(true);
          } else {
            setDropdownOpen(false);
            event.preventDefault();
          }

          onKeyDown?.(event);
        }
      },
      [onKeyDown],
    );

    const handleFocus = useCallback(() => {
      onFocus?.();
    }, [onFocus]);

    const handleBlur = useCallback(() => {
      onBlur?.();
    }, [onBlur]);

    const selectOptions = options.map((option) => ({
      label: (
        <div className="flex items-center gap-2">
          <div
            className="min-w-4 h-4 rounded border border-gray-300"
            style={{ backgroundColor: option.hexCode }}
          />
          <span>{option.name}</span>
        </div>
      ),
      value: option.id,
    }));

    return (
      <Controller
        name={name}
        control={control}
        rules={{
          required: mandatory ? "This field is required" : false,
        }}
        render={({ field, fieldState: { error } }) => (
          <div className="w-full">
            <Select
              {...field}
              id={id}
              disabled={disabled}
              placeholder="Select color"
              className={`w-full ${className || ""}`}
              options={selectOptions}
              allowClear
              showSearch
              open={dropdownOpen}
              onDropdownVisibleChange={(open) => {
                setDropdownOpen(open);
              }}
              virtual={selectOptions.length > 100}
              filterOption={(input, option) => {
                const colorOption = options.find(
                  (opt) => opt.id === option?.value,
                );
                return (
                  colorOption?.name
                    .toLowerCase()
                    .includes(input.toLowerCase()) || false
                );
              }}
              onKeyDown={handleKeyDown}
              onFocus={handleFocus}
              onBlur={handleBlur}
              value={field.value || undefined}
              onChange={(value) => {
                // Ensure we handle undefined/null values properly when clearing
                const finalValue =
                  value === undefined || value === null ? null : value;
                field.onChange(finalValue);

                // Trigger row status update
                if (
                  onFieldChange &&
                  typeof rowIndex === "number" &&
                  fieldPath
                ) {
                  onFieldChange(rowIndex, fieldPath, finalValue);
                }
              }}
              onClear={() => {
                // Explicitly handle clear action
                field.onChange(null);
                if (
                  onFieldChange &&
                  typeof rowIndex === "number" &&
                  fieldPath
                ) {
                  onFieldChange(rowIndex, fieldPath, null);
                }
              }}
            />
            <ErrorTooltip error={error} />
          </div>
        )}
      />
    );
  },
);

FieldColour.displayName = "FieldColour";
