import { RequestState } from "@/common/configs/app.contants";
import { appRequest } from "@/common/configs/app.di-container";
import { getErrorMessage } from "@/utils/error.utils";

import { MobileProfileQuery } from "../interface/mobile-profile.query";
import { MobileProfileBodyType } from "../model/schema/mobile-profile.schema";

const mobileProfileRequest = {
  getList: async (params: MobileProfileQuery) => {
    try {
      const response = await appRequest.get<any>(
        `/services/app/MobileProfile/GetAll`,
        params,
      );
      return {
        state: RequestState.success,
        data: {
          items: response?.result?.items,
          pagination: {
            current:
              Math.floor(
                (params?.skipCount ?? 1) / (params?.maxResultCount ?? 10),
              ) + 1,
            pageSize: params?.maxResultCount ?? 10,
            total: response?.result?.totalCount,
          },
        },
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  getMobileProfile: async (params: { Id: string }) => {
    try {
      const response = await appRequest.get<any>(
        `/services/app/MobileProfile/Get?Id=${params.Id}`,
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },

  create: async (body: MobileProfileBodyType) => {
    try {
      const response = await appRequest.post<any>(
        `/services/app/MobileProfile/Create`,
        body,
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  delete: async (params: { id: string }) => {
    try {
      const response = await appRequest.delete<any>(
        `/services/app/MobileProfile/Delete?Id=${params.id}`,
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },

  update: async (body: MobileProfileBodyType) => {
    try {
      const response = await appRequest.put<any>(
        `/services/app/MobileProfile/Update`,
        body,
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
};

export default mobileProfileRequest;
