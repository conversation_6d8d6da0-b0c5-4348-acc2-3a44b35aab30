import { useMemo } from "react";

import { ScreenConfig } from "../interfaces/ui/responsive";

function useTailwindResponsive(screenConfig: ScreenConfig) {
  const colsClassName = useMemo(() => {
    return Object.keys(screenConfig)
      .map((colSpan) => {
        if (colSpan === "default") {
          return `col-span-${screenConfig[colSpan]?.col}`;
        }
        return `${colSpan}:col-span-${screenConfig[colSpan].col}`;
      })
      .join(" ");
  }, [screenConfig]);

  const gridsClassName = useMemo(() => {
    return Object.keys(screenConfig)
      .map((colSpan) => {
        if (colSpan === "default") {
          return `grid-cols-${screenConfig[colSpan]?.grid}`;
        }
        return `${colSpan}:grid-cols-${screenConfig[colSpan].grid}`;
      })
      .join(" ");
  }, [screenConfig]);

  return {
    colsClass: colsClassName,
    gridsClass: gridsClassName,
  };
}

export default useTailwindResponsive;
