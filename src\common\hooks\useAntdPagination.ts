/* eslint-disable react-hooks/exhaustive-deps */
"use client";
import { TablePaginationConfig } from "antd";
import { cloneDeep, isArray, isEqual } from "lodash";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

import { useAppDispatch } from "@/common/vendors/redux/store/hook";

import { RequestState } from "../configs/app.contants";
import { IPaginationResponse } from "../interfaces/response/IPaginationResponse";

interface UseAntdPaginationProps {
  reduxTableData: Array<any>;
  reduxTablePagination: IPaginationResponse;
  requestState: RequestState;
  getDataAction: any;
  filter?: Record<string, any>;
  pageSize?: number;
  pageSizeOptions?: string[];
  isMemoziedPage?: boolean;
}

export function useAntdPagination({
  reduxTableData,
  reduxTablePagination,
  requestState = RequestState.idle,
  getDataAction,
  filter = {},
  pageSize = 10,
  pageSizeOptions = ["10", "20", "30", "50", "100"],
  isMemoziedPage = true,
}: UseAntdPaginationProps) {
  const dispatch = useAppDispatch();
  const [currentQuery, setCurrentQuery] = useState(null);
  const searchParams = useSearchParams();
  const queries: any = {};
  for (const [key, value] of searchParams.entries()) {
    const arrayValues = searchParams.getAll(key);
    queries[key] = arrayValues.length > 1 ? arrayValues : value;
  }
  const router = useRouter();
  const params = new URLSearchParams(queries);
  const page = params.get("page");

  const [tablePagination, setTablePagination] = useState<TablePaginationConfig>(
    {
      current: parseInt(queries?.page ?? "1"),
      pageSize: pageSize,
      total: 0,
      pageSizeOptions,
    },
  );

  const updateQueryParam = (payloadSearch: any): void => {
    const params = new URLSearchParams();
    const newQuery = {
      ...queries,
      ...payloadSearch,
    };

    Object.keys(newQuery).forEach((key) => {
      if (!newQuery[key]) {
        return;
      }

      if (isArray(newQuery[key])) {
        newQuery[key].forEach((value) => {
          params.append(key, value);
        });
      } else {
        params.append(key, newQuery[key]);
      }
    });

    if (isEqual(newQuery, currentQuery)) {
      return;
    }

    router.replace(`${window.location.pathname}?${params.toString()}`);
  };

  const handleTableChange = (
    pagination: TablePaginationConfig,
    filters: any,
    sorter: any,
  ) => {
    let newFilter = {};
    if (Object.keys(queries).length > 0) {
      newFilter = queries || "";
    }

    const sortParams: any = {};
    if (sorter?.field && sorter?.order) {
      sortParams.sortField = sorter.field;
      sortParams.sortOrder = sorter.order === "descend" ? "desc" : "asc";
    }

    const payloadSearch = {
      ...filter,
      ...newFilter,
      ...sortParams,
      page: pagination?.current ?? 1,
      pageSize: pagination?.pageSize ?? pageSize,
    };

    const newPayloadSearch = cloneDeep(payloadSearch);
    delete newPayloadSearch["pageSize" as any];
    newPayloadSearch["page" as any] = newPayloadSearch.page.toString();

    if (pagination.current) {
      params.set("page", pagination.current.toString());
      if (sortParams.sortField) {
        params.set("sortField", sortParams.sortField);
        params.set("sortOrder", sortParams.sortOrder);
      }
      router.replace(`${window.location.pathname}?${params.toString()}`);

      setTablePagination({
        ...tablePagination,
        ...pagination,
      });
    }

    dispatch(getDataAction(payloadSearch));
    setTablePagination({
      ...tablePagination,
      ...pagination,
    });

    if (!isMemoziedPage) {
      return;
    }

    updateQueryParam(payloadSearch);
  };

  function refresh(params?: any) {
    if (params) {
      dispatch(getDataAction(params));
    } else {
      return handleTableChange(tablePagination, {}, {});
    }
  }

  useEffect(() => {
    if (requestState === RequestState.success) {
      setTablePagination({
        current: Number(page) ?? 1,
        pageSize: pageSize,
        total: reduxTablePagination?.total ?? 0,
        showQuickJumper: true,
        pageSizeOptions: pageSizeOptions,
      });
    }
  }, [requestState, reduxTablePagination?.total]);

  useEffect(() => {
    setCurrentQuery(queries as any);
    handleTableChange(
      {
        pageSize: pageSize,
        current: parseInt((queries?.page as any) ?? "1"),
      },
      {},
      {},
    );
  }, [JSON.stringify(queries)]);

  return {
    reduxTableData,
    updateQueryParam,
    handleTableChange,
    tablePagination,
    setTablePagination,
    refresh,
    queries,
  };
}
