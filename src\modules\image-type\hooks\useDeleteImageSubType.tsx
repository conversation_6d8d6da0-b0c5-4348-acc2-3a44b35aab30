import { useState } from "react";

import imageSubTypeRequest from "../api/image-subtype.api";

export const useDeleteImageSubType = () => {
  const [loading, setLoading] = useState(false);

  async function request(
    params: {
      id: string;
    },
    onSuccess?: Function,
    onError?: Function,
  ) {
    setLoading(true);
    const response = await imageSubTypeRequest.delete(params);
    if (response.state === "success") {
      onSuccess?.(response.data);
      setLoading(false);
    } else {
      onError?.(response);
      setLoading(false);
    }
  }
  return {
    request,
    loading,
  };
};
