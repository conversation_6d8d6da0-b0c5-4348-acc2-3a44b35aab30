/* eslint-disable react-hooks/exhaustive-deps */
import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";

import { useDebounce } from "@/hooks/useDebounce";

import imageTypeRequest from "../api/image-type.api";
import { ImageTypeQuery } from "../interface/image-type.query";

interface ScrollEvent {
  target: {
    scrollTop: number;
    offsetHeight: number;
    scrollHeight: number;
  };
}

export const useQueryImageType = () => {
  const [enable, setEnable] = useState(true);
  const [isEnd, setIsEnd] = useState(false);
  const isFirstRender = useRef(true);
  const [searchParams, setSearchParams] = useState<ImageTypeQuery>({
    isActive: true,
    maxResultCount: 100,
    skipCount: 0,
  });

  // Memoize search params to prevent unnecessary re-renders
  const memoizedSearchParams = useMemo(
    () => searchParams,
    [
      searchParams.isActive,
      searchParams.maxResultCount,
      searchParams.skipCount,
      searchParams.keyword,
      searchParams.projectId,
      searchParams.prospectId,
    ],
  );

  // Throttled scroll handler with proper pagination
  const handleScroll = useCallback(
    (event: ScrollEvent) => {
      const target = event.target;
      const isAtBottom =
        target.scrollTop + target.offsetHeight >= target.scrollHeight - 1;

      if (isAtBottom && !isEnd) {
        setSearchParams((prev) => ({
          ...prev,
          maxResultCount: (prev.maxResultCount ?? 0) + 10,
        }));
      }
    },
    [isEnd],
  );

  // Reset pagination when keyword changes (fixed dependency issue)
  useEffect(() => {
    // Skip the first render to prevent unnecessary API calls
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }

    // Only reset if keyword actually changes from a meaningful value
    // This prevents reset when keyword changes from undefined to "" on initial load
    if (searchParams.keyword !== undefined) {
      setIsEnd(false);
      setSearchParams((prev) => ({
        ...prev,
        skipCount: 0,
        maxResultCount: 100,
      }));
    }
  }, [searchParams.keyword]);

  const debouncedSearch = useDebounce(searchParams?.keyword, 500);

  const { data, isLoading, error, refetch } = useQuery({
    queryKey: [
      "image-types",
      { ...memoizedSearchParams, keyword: debouncedSearch },
    ],
    queryFn: () =>
      imageTypeRequest.getList({
        ...memoizedSearchParams,
        keyword: debouncedSearch,
      }),
    enabled: enable && !isEnd,
    placeholderData: keepPreviousData,
  });

  // Improved end detection logic
  useEffect(() => {
    if (data?.data?.pagination) {
      const { total } = data.data.pagination;
      const currentCount = searchParams.maxResultCount ?? 0;

      if (currentCount >= total) {
        setIsEnd(true);
      } else {
        setIsEnd(false);
      }
    }
  }, [data, searchParams.maxResultCount]);

  return {
    data,
    isLoading,
    error,
    refetch,
    setSearchParams,
    searchParams: memoizedSearchParams,
    setEnable,
    handleScroll,
    isEnd,
    enable,
  };
};
