import PermissionProvider from "@/components/layout/permission-provider";
import { PERMISSIONS } from "@/constants/general.const";
import TableWorkflows from "@/modules/workflows/components/table-workflows";

export default function WorkflowPage() {
  return (
    <PermissionProvider
      permissions={[
        PERMISSIONS.Company,
        PERMISSIONS.EditorUser,
        PERMISSIONS.ViewerUser,
      ]}
    >
      <TableWorkflows />
    </PermissionProvider>
  );
}
