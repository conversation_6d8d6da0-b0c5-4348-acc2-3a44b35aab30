import { But<PERSON>, <PERSON> } from "antd";
import { useEffect, useState } from "react";
import { UseFormReturn } from "react-hook-form";

import { useQueryColour } from "@/modules/list/hooks/colour/useQueryColour";

export const ColorFill = ({
  form,
  selectedColumn,
  setIsModalOpen,
}: {
  form: UseFormReturn<any>;
  selectedColumn: string;
  setIsModalOpen: (isModalOpen: boolean) => void;
}) => {
  const { data: colours, setSearchParams, searchParams } = useQueryColour();
  const handleScrollColour = (event: any) => {
    const target = event.target;
    if (target.scrollTop + target.offsetHeight === target.scrollHeight) {
      setSearchParams((prev: any) => ({
        ...prev,
        maxResultCount: prev?.maxResultCount + 10,
      }));
    }
  };
  const handleFillColumn = (keyName: string, value: any) => {
    form.watch("dataUpload").forEach((d: any, index: number) => {
      if (!d[keyName]) {
        form.setValue(`dataUpload.${index}.${keyName}`, value);
      }
    });
    setIsModalOpen(false);
  };
  useEffect(() => {
    setFillValue("");
  }, []);
  const [fillValue, setFillValue] = useState("");
  return (
    <div className="flex flex-col gap-4">
      <Select
        options={colours?.data?.items?.map((d) => ({
          label: d.name,
          value: d.name,
        }))}
        value={fillValue}
        onChange={(value) => setFillValue(value)}
        className="w-full"
        onPopupScroll={handleScrollColour}
        showSearch
        allowClear
        onSearch={(value) => {
          setSearchParams((prev: any) => ({
            ...prev,
            keyword: value,
          }));
        }}
        onClear={() => {
          setSearchParams((prev: any) => ({
            ...prev,
            keyword: "",
          }));
        }}
        searchValue={searchParams?.keyword}
        filterOption={false}
        placeholder="Select color"
      />
      <Button onClick={() => handleFillColumn(selectedColumn, fillValue)}>
        Fill Color
      </Button>
    </div>
  );
};
