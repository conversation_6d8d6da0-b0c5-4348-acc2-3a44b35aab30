import { useState } from "react";

import imageTypeRequest from "../api/image-type.api";

export const useDeleteImageType = () => {
  const [loading, setLoading] = useState(false);

  async function request(
    params: {
      id: string;
    },
    onSuccess?: Function,
    onError?: Function,
  ) {
    setLoading(true);
    const response = await imageTypeRequest.delete(params);
    if (response.state === "success") {
      onSuccess?.(response.data);
      setLoading(false);
    } else {
      onError?.(response);
      setLoading(false);
    }
  }
  return {
    request,
    loading,
  };
};
