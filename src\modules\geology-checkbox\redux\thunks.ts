import { createAppAsyncThunk } from "@/common/vendors/redux/store/createAppAsyncThunk";

import geologyCheckboxRequest from "../api/geology-checkbox.api";
import { GeologyCheckboxQuery } from "../interface/geology-checkbox.query";

export const getListGeologyCheckbox = createAppAsyncThunk(
  "geologyCheckbox/list",
  async (query: GeologyCheckboxQuery) => {
    const { page = 1, pageSize = 10, ...otherQueries } = query;
    const response = await geologyCheckboxRequest.getList({
      skipCount: (page - 1) * pageSize,
      maxResultCount: pageSize,
      ...otherQueries,
    });

    return response;
  },
);

export const getDetailGeologyCheckbox = createAppAsyncThunk(
  "geologyCheckbox/detail",
  async (id: string) => {
    const response = await geologyCheckboxRequest.getDetail(id);
    return response.data;
  },
);
