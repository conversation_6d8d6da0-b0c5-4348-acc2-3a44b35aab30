import { injectable } from "inversify";

import { appConfigs } from "@/common/configs/app.configs";
import ILogger from "@/common/interfaces/logger/ILogger";

@injectable()
export class AppLogger implements ILogger {
  log(...args: any): void {
    appConfigs.logPipes.forEach((logPipe: ILogger) => {
      logPipe.log(...args);
    });
  }
  error(...args: any): void {
    appConfigs.logPipes.forEach((logPipe: ILogger) => {
      logPipe.error(...args);
    });
  }
  warn(...args: any): void {
    appConfigs.logPipes.forEach((logPipe: ILogger) => {
      logPipe.warn(...args);
    });
  }
  debug(...args: any): void {
    appConfigs.logPipes.forEach((logPipe: ILogger) => {
      logPipe.debug(...args);
    });
  }
}
