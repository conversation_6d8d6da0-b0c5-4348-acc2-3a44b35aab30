import { useState } from "react";

import geologyLongitudeRequest from "../api/geology-longitude.api";
import { GeologyLongitudeQuery } from "../interface/geology-longitude.query";

export const useGetListGeologyLongitude = () => {
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any[]>([]);
  const request = async (
    params: GeologyLongitudeQuery,
    onSuccess?: Function,
    onError?: Function,
  ) => {
    setLoading(true);
    const response = await geologyLongitudeRequest.getList({
      ...params,
      isActive: params.isActive ?? true,
    });
    if (response?.state === "success") {
      setData(response.data?.items);
      setTotal(response.data?.pagination?.total);
      setLoading(false);
      onSuccess?.(response.data);
      return response.data;
    } else {
      onError?.(response);
      setLoading(false);
      return null;
    }
  };

  return { request, loading, data, total };
};
