import { useState } from "react";

import geologyCheckboxRequest from "../api/geology-checkbox.api";

export const useDeleteGeologyCheckbox = () => {
  const [loading, setLoading] = useState(false);

  async function request(
    params: {
      id: string;
    },
    onSuccess?: Function,
    onError?: Function,
  ) {
    setLoading(true);
    const response = await geologyCheckboxRequest.delete(params);
    if (response.state === "success") {
      onSuccess?.(response.data);
      setLoading(false);
    } else {
      onError?.(response);
      setLoading(false);
    }
  }
  return {
    request,
    loading,
  };
};
