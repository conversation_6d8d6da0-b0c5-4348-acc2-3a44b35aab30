import { useState } from "react";

import geologyLatitudeRequest from "../api/geology-latitude.api";
import { GeologyLatitudeBodyType } from "../model/schema/geology-latitude.schema";

export const useCreateGeologyLatitude = () => {
  const [loading, setLoading] = useState(false);
  async function request(
    params: GeologyLatitudeBodyType,
    onSuccess?: Function,
    onError?: Function,
  ) {
    setLoading(true);
    const response = await geologyLatitudeRequest.create(params);
    if (response.state === "success") {
      onSuccess?.(response.data);
      setLoading(false);
    } else {
      onError?.(response);
      setLoading(false);
    }
  }
  return {
    request,
    loading,
  };
};
