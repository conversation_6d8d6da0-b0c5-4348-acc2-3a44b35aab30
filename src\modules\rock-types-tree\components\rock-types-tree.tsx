/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable max-lines */
/* eslint-disable max-lines-per-function */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable complexity */
"use client";

import "./rock-types-tree.css";

import { Divider } from "antd";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { NodeApi, Tree, TreeApi } from "react-arborist";
import { toast } from "react-toastify";

// @ts-ignore
import { RequestState } from "@/common/configs/app.contants";

import rockTreeRequest, {
  TreeNode as ApiTreeNode,
  RockNodeDto,
} from "../api/rock-tree.api";
import RockNodeEditor from "./rock-node-editor";
import RockTreeActions from "./rock-tree-actions";
import RockTreeHeader from "./rock-tree-header";
import {
  ComponentTreeNode as RockTreeComponentTreeNode,
  RockTreeNode,
} from "./rock-tree-node";
import RockTreeSearch from "./rock-tree-search";
import RockTreeStatus from "./rock-tree-status";

interface RockTypesTreeProps {}

const RockTypesTree: React.FC<RockTypesTreeProps> = () => {
  const [treeData, setTreeData] = useState<RockTreeComponentTreeNode[]>([]);
  const [loading, setLoading] = useState(false); // This will primarily indicate initial load
  const [loadType, setLoadType] = useState<"initial" | "background" | null>(
    null,
  );
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredData, setFilteredData] = useState<RockTreeComponentTreeNode[]>(
    [],
  );
  const [editorOpen, setEditorOpen] = useState(false);
  const [editNode, setEditNode] = useState<RockNodeDto | null>(null);
  const [editorMode, setEditorMode] = useState<
    "create-folder" | "create-rock" | "edit" | null
  >(null);
  const [editParentId, setEditParentId] = useState<number | null>(null);
  const [pendingSuccessAction, setPendingSuccessAction] = useState<
    "create" | "edit" | "delete" | null
  >(null);

  interface OriginalNodePositionInfo {
    nodeId: string;
    parentId: string | null;
    originalIndex: number;
    nodeData: RockTreeComponentTreeNode;
  }
  const [originalNodePosition, setOriginalNodePosition] =
    useState<OriginalNodePositionInfo | null>(null);
  const [isMovingNode, setIsMovingNode] = useState(false);

  const transformToComponentTreeNodes = useCallback(
    (apiNodes: ApiTreeNode[]): RockTreeComponentTreeNode[] => {
      return apiNodes.map((apiNode) => {
        const rockNodeData: RockNodeDto = {
          id: apiNode.id,
          name: apiNode.name,
          code: apiNode.code ?? undefined,
          description: apiNode.description ?? undefined,
          nodeType: apiNode.nodeType,
          rockTypeId: apiNode.rockTypeId ?? undefined,
          isActive:
            typeof apiNode.isActive === "boolean" ? apiNode.isActive : true,
          displayColor: apiNode.displayColor ?? undefined,
          iconUrl: apiNode.iconUrl ?? undefined,
          parentNodeId: apiNode.parentId ?? undefined,
          order: 0,
        };
        return {
          id: apiNode.id.toString(),
          name: apiNode.name,
          isFolder: apiNode.nodeType === 0,
          children: apiNode.children
            ? transformToComponentTreeNodes(apiNode.children)
            : [],
          data: rockNodeData,
          isOpen: false,
        };
      });
    },
    [],
  );

  // Moved searchNodesRecursive before loadTreeData because loadTreeData depends on it.
  const searchNodesRecursive = useCallback(
    (
      nodesToSearch: RockTreeComponentTreeNode[],
      term: string,
      forceOpen = true,
    ): RockTreeComponentTreeNode[] => {
      if (!term.trim()) {
        return nodesToSearch.map((n) => ({
          ...n,
          isOpen: n.isOpen, // Keep original isOpen state if not forcing open
          children: n.children
            ? searchNodesRecursive(
                n.children as RockTreeComponentTreeNode[],
                term,
                false, // Don't force open children if parent isn't a match initially
              )
            : [],
        }));
      }
      return nodesToSearch
        .map((node) => {
          const matches = node.name.toLowerCase().includes(term.toLowerCase());
          const matchingChildren = node.children
            ? searchNodesRecursive(
                node.children as RockTreeComponentTreeNode[],
                term,
                forceOpen, // Propagate forceOpen to children
              )
            : [];
          if (matches || matchingChildren.length > 0) {
            return {
              ...node,
              children: matchingChildren,
              isOpen: forceOpen ? true : node.isOpen, // Only force open if specified
            };
          }
          return null;
        })
        .filter(Boolean) as RockTreeComponentTreeNode[];
    },
    [], // searchNodesRecursive does not have external dependencies other than its own recursive calls
  );

  const loadTreeData = useCallback(
    async (isBackgroundRefresh: boolean = false) => {
      if (!isBackgroundRefresh) {
        setLoading(true);
        setLoadType("initial");
      } else {
        setLoadType("background");
      }
      setError(null);
      try {
        const result = await rockTreeRequest.getTenantTreeStructure();
        if (result.state === RequestState.success && result.data) {
          const transformedNodes = transformToComponentTreeNodes(
            result.data.rootNodes,
          );
          setTreeData(transformedNodes);
          if (searchTerm) {
            // When loading data (initial or background), if there's a search term,
            // re-apply the search to the newly fetched data.
            // Pass `true` for forceOpen to ensure matched items are visible.
            setFilteredData(
              searchNodesRecursive(transformedNodes, searchTerm, true),
            );
          } else {
            // If no search term, the filtered data is the same as the full tree data.
            // Ensure nodes are not all forced open by default after a refresh without search.
            setFilteredData(searchNodesRecursive(transformedNodes, "", false));
          }
        } else {
          const errorMessage = result.message || "Failed to load tree data.";
          toast.error(errorMessage);
          setError(errorMessage);
          setTreeData([]);
          setFilteredData([]);
        }
      } catch {
        const errorMessage =
          "An unexpected error occurred while loading the tree data.";
        toast.error(errorMessage);
        setError(errorMessage);
        setTreeData([]);
        setFilteredData([]);
      } finally {
        if (!isBackgroundRefresh) {
          setLoading(false);
        }
        setLoadType(null);
      }
    },
    [transformToComponentTreeNodes, searchTerm, searchNodesRecursive],
  );

  const handleSearch = useCallback(
    (value: string) => {
      setSearchTerm(value);
      if (!value.trim()) {
        setFilteredData(
          treeData.map((node) => ({
            ...node,
            isOpen: false,
            children: node.children
              ? searchNodesRecursive(
                  node.children as RockTreeComponentTreeNode[],
                  "",
                  false,
                )
              : [],
          })),
        );
        return;
      }
      setFilteredData(searchNodesRecursive(treeData, value, true));
    },
    [treeData, searchNodesRecursive],
  );

  const handleSaveNode = useCallback(
    async (nodeData: any, currentEditorMode: string | null) => {
      if (!currentEditorMode) {
        return;
      }
      setError(null);
      const savingToastId = toast.loading(`Saving node...`);
      try {
        let result;
        const payload = { ...nodeData };
        if (
          (currentEditorMode === "create-folder" ||
            currentEditorMode === "create-rock") &&
          editParentId
        ) {
          payload.parentNodeId = editParentId;
        } else if (
          (currentEditorMode === "create-folder" ||
            currentEditorMode === "create-rock") &&
          !editParentId
        ) {
          delete payload.rockTreeRootId;
        }

        if (currentEditorMode === "edit" && editNode) {
          result = await rockTreeRequest.updateNode({
            id: editNode.id,
            ...payload,
          });
        } else if (currentEditorMode === "create-folder") {
          result = await rockTreeRequest.createFolderNode(payload);
        } else if (currentEditorMode === "create-rock") {
          result = await rockTreeRequest.createRockTypeNode(payload);
        }

        toast.dismiss(savingToastId);
        if (result && result.state === RequestState.success) {
          setPendingSuccessAction(
            currentEditorMode === "edit" ? "edit" : "create",
          );
          loadTreeData(true); // Background refresh
        } else {
          const errorMsg =
            result?.message ||
            `Failed to ${
              currentEditorMode === "edit" ? "update" : "create"
            } node`;
          toast.error(errorMsg);
          setError(errorMsg);
        }
      } catch {
        toast.dismiss(savingToastId);
        const errorMsg = `An error occurred while ${
          currentEditorMode === "edit" ? "updating" : "creating"
        } the node`;
        toast.error(errorMsg);
        setError(errorMsg);
      } finally {
        setEditorOpen(false);
        setEditNode(null);
        setEditorMode(null);
        setEditParentId(null);
      }
    },
    [editNode, editParentId, loadTreeData],
  );

  const handleDeleteNode = useCallback(
    async (nodeToDelete: RockNodeDto) => {
      setError(null);
      const deletingToastId = toast.loading("Deleting node...");
      try {
        const result = await rockTreeRequest.deleteNode(nodeToDelete.id);
        toast.dismiss(deletingToastId);
        if (result.state === RequestState.success) {
          setPendingSuccessAction("delete");
          loadTreeData(true); // Background refresh
        } else {
          const errorMsg = result.message || "Failed to delete node";
          toast.error(errorMsg);
          setError(errorMsg);
        }
      } catch {
        toast.dismiss(deletingToastId);
        const errorMsg = "An error occurred while deleting the node";
        toast.error(errorMsg);
        setError(errorMsg);
      }
    },
    [loadTreeData],
  );

  const findNodeRecursive = useCallback(
    (
      nodesToSearch: RockTreeComponentTreeNode[],
      targetId: string,
      currentParentId: string | null = null,
    ): {
      node: RockTreeComponentTreeNode;
      parentId: string | null;
      index: number;
    } | null => {
      for (let i = 0; i < nodesToSearch.length; i++) {
        const node = nodesToSearch[i];
        if (node.id === targetId) {
          return { node, parentId: currentParentId, index: i };
        }
        if (node.children && node.children.length > 0) {
          const foundInChildren = findNodeRecursive(
            node.children as RockTreeComponentTreeNode[],
            targetId,
            node.id,
          );
          if (foundInChildren) {
            return foundInChildren;
          }
        }
      }
      return null;
    },
    [],
  );

  const findAndRemoveNodeRecursive = useCallback(
    (
      nodes: RockTreeComponentTreeNode[],
      targetId: string,
    ): {
      removedNode: RockTreeComponentTreeNode | null;
      updatedNodes: RockTreeComponentTreeNode[];
    } => {
      let removedNodeInstance: RockTreeComponentTreeNode | null = null;
      const remainingNodes = nodes.filter((node) => {
        if (node.id === targetId) {
          removedNodeInstance = node;
          return false;
        }
        if (node.children && node.children.length > 0) {
          const result = findAndRemoveNodeRecursive(
            node.children as RockTreeComponentTreeNode[],
            targetId,
          );
          if (result.removedNode) {
            removedNodeInstance = result.removedNode;
            node.children = result.updatedNodes;
          }
        }
        return true;
      });
      return { removedNode: removedNodeInstance, updatedNodes: remainingNodes };
    },
    [],
  );

  const findParentAndInsertRecursive = useCallback(
    (
      nodes: RockTreeComponentTreeNode[],
      parentId: string,
      nodeToInsert: RockTreeComponentTreeNode,
      newIndex: number,
    ): boolean => {
      for (const node of nodes) {
        if (node.id === parentId) {
          if (!node.children) {
            node.children = [];
          }
          (node.children as RockTreeComponentTreeNode[]).splice(
            newIndex,
            0,
            nodeToInsert,
          );
          return true;
        }
        if (node.children && node.children.length > 0) {
          if (
            findParentAndInsertRecursive(
              node.children as RockTreeComponentTreeNode[],
              parentId,
              nodeToInsert,
              newIndex,
            )
          ) {
            return true;
          }
        }
      }
      return false;
    },
    [],
  );

  const handleAddRootFolder = useCallback(() => {
    setEditorMode("create-folder");
    setEditParentId(null);
    setEditNode(null);
    setEditorOpen(true);
  }, []);

  const handleEditorCancel = useCallback(() => {
    setEditorOpen(false);
    setEditNode(null);
    setEditorMode(null);
    setEditParentId(null);
  }, []);

  const handleEditorSave = useCallback(
    (data: any) => {
      handleSaveNode(data, editorMode);
    },
    [handleSaveNode, editorMode],
  );

  const updateOpenStateRecursive = useCallback(
    (
      nodes: RockTreeComponentTreeNode[],
      isOpen: boolean,
    ): RockTreeComponentTreeNode[] => {
      return nodes.map((node) => ({
        ...node,
        isOpen,
        children: node.children
          ? updateOpenStateRecursive(
              node.children as RockTreeComponentTreeNode[],
              isOpen,
            )
          : [],
      }));
    },
    [],
  );

  const treeRef = useRef<TreeApi<RockTreeComponentTreeNode>>(null);

  const handleExpandAll = useCallback(() => {
    if (treeRef.current) {
      const tree = treeRef.current;

      const recursivelyOpenNode = (
        nodeApi: NodeApi<RockTreeComponentTreeNode>,
      ) => {
        // A node is a branch if it's not a leaf.
        // Open it if it's a branch and currently closed.
        if (!nodeApi.isLeaf && !nodeApi.isOpen) {
          nodeApi.open();
        }

        // After attempting to open (or if already open),
        // recurse on its children if they exist.
        if (nodeApi.children) {
          nodeApi.children.forEach(recursivelyOpenNode);
        }
      };

      // Iterate through the currently visible top-level nodes in filteredData
      filteredData.forEach((dataNode) => {
        // We need to ensure we are getting the NodeApi instance from the tree
        const nodeApi = tree.get(dataNode.id);
        if (nodeApi) {
          recursivelyOpenNode(nodeApi);
        }
      });

      // Update our React state to match the tree's visual state.
      // This ensures our local `isOpen` flags are set correctly after react-arborist has processed opens.
      setFilteredData((prevData) => updateOpenStateRecursive(prevData, true));
    }
  }, [filteredData, updateOpenStateRecursive]);

  const handleCollapseAll = useCallback(() => {
    if (treeRef.current) {
      const commandCloseRecursive = (nodes: RockTreeComponentTreeNode[]) => {
        nodes.forEach((dataNode) => {
          if (dataNode.isFolder) {
            // Only try to close folders
            const nodeApi = treeRef.current?.get(dataNode.id);
            if (nodeApi && nodeApi.isOpen) {
              nodeApi.close();
            }
          }
          // Recurse to ensure children are also processed if direct child closing is needed,
          // though closing a parent typically handles its descendants.
          // For explicit state update, this ensures all nodes in `filteredData` are marked.
          if (dataNode.children && dataNode.children.length > 0) {
            commandCloseRecursive(
              dataNode.children as RockTreeComponentTreeNode[],
            );
          }
        });
      };
      // It's generally sufficient to command the top-level nodes in filteredData to close,
      // as react-arborist should handle children. However, to be thorough for our state update:
      commandCloseRecursive(filteredData);

      // Update our React state to match the tree's visual state
      setFilteredData((prevData) => updateOpenStateRecursive(prevData, false));
    }
  }, [filteredData, updateOpenStateRecursive]);

  const handleMoveNode = useCallback(
    async ({
      dragIds,
      parentId: newParentNodeIdStr,
      index: newIndex,
    }: {
      dragIds: string[];
      parentId: string | null;
      index: number;
    }) => {
      if (isMovingNode) {
        return;
      }
      if (dragIds.length > 0) {
        const draggedNodeId = dragIds[0];
        const originalInfo = findNodeRecursive(treeData, draggedNodeId);

        if (originalInfo) {
          setOriginalNodePosition({
            nodeId: draggedNodeId,
            parentId: originalInfo.parentId,
            originalIndex: originalInfo.index,
            nodeData: JSON.parse(JSON.stringify(originalInfo.node)),
          });
        } else {
          toast.error("Error preparing node move: Original data not found.");
          setOriginalNodePosition(null);
          return;
        }

        const performOptimisticUpdate = (
          currentTreeState: RockTreeComponentTreeNode[],
        ): RockTreeComponentTreeNode[] => {
          let newClonedData = JSON.parse(JSON.stringify(currentTreeState));
          const { removedNode, updatedNodes } = findAndRemoveNodeRecursive(
            newClonedData,
            draggedNodeId,
          );

          if (!removedNode) {
            return currentTreeState;
          }
          newClonedData = updatedNodes;

          if (newParentNodeIdStr === null) {
            newClonedData.splice(newIndex, 0, removedNode);
          } else {
            findParentAndInsertRecursive(
              newClonedData,
              newParentNodeIdStr,
              removedNode,
              newIndex,
            );
          }
          return newClonedData;
        };

        setTreeData((prevTreeData) => performOptimisticUpdate(prevTreeData));
        setFilteredData((prevFilteredData) =>
          performOptimisticUpdate(prevFilteredData),
        );

        setIsMovingNode(true);
        let savingToastId: string | number | undefined;

        try {
          savingToastId = toast.loading("Saving node position...");
          const nodeIdToMove = parseInt(draggedNodeId, 10);
          const targetParentId = newParentNodeIdStr
            ? parseInt(newParentNodeIdStr, 10)
            : null;

          if (isNaN(nodeIdToMove)) {
            throw new Error("Invalid node ID for moving.");
          }
          if (newParentNodeIdStr && isNaN(targetParentId as number)) {
            throw new Error("Invalid new parent node ID.");
          }

          const result = await rockTreeRequest.moveNode({
            nodeIdToMove: nodeIdToMove,
            newParentNodeId: targetParentId,
            newOrder: newIndex,
          });

          toast.dismiss(savingToastId);

          if (result.state === RequestState.success) {
            toast.success("Node position saved!");
            setOriginalNodePosition(null);
            // No full tree reload here, optimistic update is considered final on success
            // If a full refresh is desired after move, call loadTreeData(true)
          } else {
            throw new Error(result.message || "Failed to save node position.");
          }
        } catch (apiError: any) {
          if (savingToastId) {
            toast.dismiss(savingToastId);
          }
          toast.error(
            `Failed to save node position: ${apiError.message}. Reverting changes.`,
          );

          // Revert optimistic update
          if (originalNodePosition && originalNodePosition.nodeData) {
            const {
              parentId: originalParentId,
              originalIndex: nodeOriginalIndex,
              nodeData: originalNodeDataItem,
              nodeId: originalNodeId,
            } = originalNodePosition;

            const performRollback = (
              currentTreeToRollback: RockTreeComponentTreeNode[],
            ): RockTreeComponentTreeNode[] => {
              let treeCopy = JSON.parse(JSON.stringify(currentTreeToRollback));
              // First, remove the node from its current (optimistically updated) position
              const removalResult = findAndRemoveNodeRecursive(
                treeCopy,
                originalNodeId,
              );
              treeCopy = removalResult.updatedNodes;

              // Then, re-insert it into its original position
              if (originalParentId === null) {
                treeCopy.splice(nodeOriginalIndex, 0, originalNodeDataItem);
              }
              return treeCopy;
            };

            setTreeData(performRollback);
            setFilteredData(performRollback); // Apply rollback to filteredData as well
          } else {
            // If originalNodePosition is not available, a full reload might be the safest fallback
            loadTreeData();
          }
          setOriginalNodePosition(null);
        } finally {
          setIsMovingNode(false);
          if (savingToastId) {
            toast.dismiss(savingToastId);
          }
          // Consider if a background refresh is needed even on successful move
          // loadTreeData(true); // If you want to ensure data consistency after move
        }
      }
    },
    [
      isMovingNode,
      treeData, // Ensures handleMoveNode closes over the latest treeData for initial find
      searchTerm,
      findNodeRecursive,
      findAndRemoveNodeRecursive,
      findParentAndInsertRecursive,
      searchNodesRecursive,
      originalNodePosition, // dependency for rollback
      loadTreeData, // dependency for fallback reload
    ],
  );

  const renderRockTreeNode = useCallback(
    (props: {
      node: NodeApi<RockTreeComponentTreeNode>;
      style: React.CSSProperties;
      dragHandle?: React.RefObject<HTMLDivElement>;
    }) => {
      return (
        <RockTreeNode
          {...props}
          setEditorOpen={setEditorOpen}
          setEditNode={setEditNode}
          setEditorMode={setEditorMode}
          setEditParentId={setEditParentId}
          handleDeleteNode={handleDeleteNode}
          isMoving={
            isMovingNode && props.node.id === originalNodePosition?.nodeId
          }
        />
      );
    },
    [handleDeleteNode, isMovingNode, originalNodePosition?.nodeId],
  );

  useEffect(() => {
    loadTreeData(false); // Explicitly initial load
  }, []); // loadTreeData itself has dependencies now

  useEffect(() => {
    // Display success notification after tree data is updated and component re-renders
    if (pendingSuccessAction && loadType === null) {
      if (pendingSuccessAction === "create") {
        toast.success("Node created successfully");
      } else if (pendingSuccessAction === "edit") {
        toast.success("Node updated successfully");
      } else if (pendingSuccessAction === "delete") {
        toast.success("Node deleted successfully");
      }
      setPendingSuccessAction(null); // Reset after showing
    }
  }, [filteredData, pendingSuccessAction, loadType]); // Depends on filteredData to ensure UI has updated

  return (
    <div className="flex flex-col">
      <RockTreeHeader
        loading={loadType === "initial"} // Show header loading only on initial
        isMovingNode={isMovingNode}
        onAddRootFolder={handleAddRootFolder}
      />
      <Divider className="my-5" />

      <RockTreeSearch isMovingNode={isMovingNode} onSearch={handleSearch} />

      <RockTreeStatus
        loading={loading} // This is the main overlay loading
        loadType={loadType} // Pass the new loadType
        error={error}
        treeData={treeData}
        filteredData={filteredData}
        searchTerm={searchTerm}
        isMovingNode={isMovingNode}
        onAddRootFolder={handleAddRootFolder}
        onClearSearch={() => handleSearch("")}
      />

      {/* Show tree if not initial loading, or if initial loading but data exists (e.g. during background update) */}
      {/* This logic might need refinement based on desired UX during background refresh */}
      {/* The primary goal is to hide the "Loading tree structure..." overlay during background refresh */}
      {/* So, the condition for RockTreeStatus to show that overlay is key. */}
      {/* The tree itself should remain visible during background refresh. */}
      {((loadType !== "initial" && !error) ||
        (loading && treeData.length > 0)) &&
        filteredData.length > 0 && (
          <div className="flex-grow overflow-y-auto rounded-md bg-white shadow-sm border border-gray-200 p-2">
            <RockTreeActions
              isMovingNode={isMovingNode}
              onExpandAll={handleExpandAll}
              onCollapseAll={handleCollapseAll}
            />
            <div
              role="tree"
              className={`custom-tree-scroll ${
                isMovingNode ? "tree-moving" : ""
              }`}
              aria-label="Rock Types Tree"
              style={{ height: "calc(100vh - 340px)" }} // Adjusted height
            >
              <Tree
                ref={treeRef}
                data={filteredData}
                width="100%"
                // height={Math.max(380, filteredData.length * 36 + 20)} // Adjusted height
                indent={24}
                rowHeight={36}
                overscanCount={10}
                paddingTop={8}
                paddingBottom={8}
                disableDrag={isMovingNode}
                className="custom-tree focusable-tree text-sm" // Added text-sm for smaller font
                childrenAccessor="children"
                idAccessor="id"
                onMove={handleMoveNode}
              >
                {renderRockTreeNode as any}
              </Tree>
            </div>
            {/* <RockTreeKeyboardShortcuts /> */}
          </div>
        )}

      {editorOpen && editorMode && (
        <div id="editor-container">
          <RockNodeEditor
            open={editorOpen}
            mode={editorMode}
            initialValues={editNode}
            onCancel={handleEditorCancel}
            onSave={handleEditorSave}
          />
        </div>
      )}
    </div>
  );
};

export default RockTypesTree;
