import { createAppAsyncThunk } from "@/common/vendors/redux/store/createAppAsyncThunk";

import geologyLatitudeRequest from "../api/geology-latitude.api";
import { GeologyLatitudeQuery } from "../interface/geology-latitude.query";

export const getListGeologyLatitude = createAppAsyncThunk(
  "geologyLatitude/list",
  async (query: GeologyLatitudeQuery) => {
    const { page = 1, pageSize = 10, ...otherQueries } = query;
    const response = await geologyLatitudeRequest.getList({
      skipCount: (page - 1) * pageSize,
      maxResultCount: pageSize,
      ...otherQueries,
    });

    return response;
  },
);

export const getDetailGeologyLatitude = createAppAsyncThunk(
  "geologyLatitude/detail",
  async (id: string) => {
    const response = await geologyLatitudeRequest.getDetail(id);
    return response.data;
  },
);
