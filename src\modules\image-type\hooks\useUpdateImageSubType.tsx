import { useState } from "react";

import imageSubTypeRequest from "../api/image-subtype.api";
import { ImageSubTypeBodyType } from "../model/schema/image-type.schema";

export const useUpdateImageSubType = () => {
  const [loading, setLoading] = useState(false);
  async function request(
    params: ImageSubTypeBodyType,
    onSuccess?: Function,
    onError?: Function,
  ) {
    setLoading(true);
    const response = await imageSubTypeRequest.update(params);
    if (response.state === "success") {
      onSuccess?.(response.data);
      setLoading(false);
    } else {
      onError?.(response);
      setLoading(false);
    }
  }
  return {
    request,
    loading,
  };
};
