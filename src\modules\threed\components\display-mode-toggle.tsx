"use client";
import { <PERSON><PERSON> } from "antd";
import React from "react";
import { useDispatch, useSelector } from "react-redux";

import { DrillholeDisplayMode } from "../interface/threed.interface";
import { selectDrillholeDisplayMode } from "../redux/threedSlice/selectors";
import { setDrillholeDisplayMode } from "../redux/threedSlice/threed.slice";

const DisplayModeToggle: React.FC = () => {
  const dispatch = useDispatch();
  const currentMode = useSelector(selectDrillholeDisplayMode);

  const handleModeChange = (mode: DrillholeDisplayMode) => {
    dispatch(setDrillholeDisplayMode(mode));
  };

  return (
    <div className="flex gap-2 items-center">
      <span className="text-sm font-medium text-gray-600">Display Mode:</span>
      <Button
        type={currentMode === "drillhole" ? "primary" : "default"}
        size="small"
        onClick={() => handleModeChange("drillhole")}
      >
        Drillhole
      </Button>
      <Button
        type={currentMode === "desurvey" ? "primary" : "default"}
        size="small"
        onClick={() => handleModeChange("desurvey")}
      >
        Desurvey
      </Button>
    </div>
  );
};

export default DisplayModeToggle;
