import { createSlice } from "@reduxjs/toolkit";

import { RequestState } from "@/common/configs/app.contants";

import { getMobileProfile } from "./thunks";

const initialState: MobileProfileSliceState = {
  status: RequestState.idle,
};

export const mobileProfileSlice = createSlice({
  name: "mobileProfile",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getMobileProfile.pending, (state) => {
        state.status = RequestState.pending;
      })
      .addCase(getMobileProfile.fulfilled, (state, action) => {
        state.status = RequestState.success;
        state.result = action.payload.data;
      });
  },
});

export interface MobileProfileSliceState {
  result?: any;
  status: RequestState;
}

export const {} = mobileProfileSlice.actions;
