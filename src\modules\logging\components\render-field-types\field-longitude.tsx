import { Input } from "antd";
import React, { memo, useCallback } from "react";
import { Control, Controller } from "react-hook-form";

import { ErrorTooltip } from "./error-tooltip";

interface FieldLongitudeProps {
  control: Control<any>;
  name: string;
  disabled?: boolean;
  mandatory?: boolean;
  onKeyDown?: (event: React.KeyboardEvent) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  className?: string;
  id?: string;
  onFieldChange?: (rowIndex: number, fieldPath: string, value: any) => void;
  rowIndex?: number;
  fieldPath?: string;
}

export const FieldLongitude = memo<FieldLongitudeProps>(
  ({
    control,
    name,
    disabled = false,
    mandatory = false,
    onKeyDown,
    onFocus,
    onBlur,
    className,
    id,
    onFieldChange,
    rowIndex,
    fieldPath,
  }) => {
    const handleKeyDown = useCallback(
      (event: React.KeyboardEvent) => {
        // Handle tab navigation and shortcuts
        onKeyDown?.(event);
      },
      [onKeyDown],
    );

    const handleFocus = useCallback(() => {
      onFocus?.();
    }, [onFocus]);

    const handleBlur = useCallback(() => {
      onBlur?.();
    }, [onBlur]);

    return (
      <Controller
        name={name}
        control={control}
        rules={{
          required: mandatory ? "This field is required" : false,
        }}
        render={({ field, fieldState: { error } }) => (
          <div className="w-full">
            <Input
              {...field}
              id={id}
              disabled={disabled}
              placeholder="Longitude"
              className={`w-full ${className || ""}`}
              onKeyDown={handleKeyDown}
              onFocus={handleFocus}
              onBlur={handleBlur}
              onChange={(e) => {
                field.onChange(e);

                // Trigger row status update
                if (
                  onFieldChange &&
                  typeof rowIndex === "number" &&
                  fieldPath
                ) {
                  onFieldChange(rowIndex, fieldPath, e.target.value);
                }
              }}
            />
            <ErrorTooltip error={error} />
          </div>
        )}
      />
    );
  },
);

FieldLongitude.displayName = "FieldLongitude";
