import { createSlice } from "@reduxjs/toolkit";

import { RequestState } from "@/common/configs/app.contants";

import { getDetailGeologyLongitude, getListGeologyLongitude } from "./thunks";

const initialState: GeologyLongitudeSliceState = {
  status: RequestState.idle,
  getDetailStatus: RequestState.idle,
};

export const geologyLongitudeSlice = createSlice({
  name: "geologyLongitude",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getListGeologyLongitude.pending, (state) => {
        state.status = RequestState.pending;
      })
      .addCase(getListGeologyLongitude.fulfilled, (state, action) => {
        state.status = RequestState.success;
        state.result = action.payload.data;
      })
      .addCase(getDetailGeologyLongitude.pending, (state) => {
        state.getDetailStatus = RequestState.pending;
      })
      .addCase(getDetailGeologyLongitude.fulfilled, (state, action) => {
        state.getDetailStatus = RequestState.success;
        state.detail = action.payload;
      });
  },
});

export interface GeologyLongitudeSliceState {
  result?: any;
  status: RequestState;
  getDetailStatus: RequestState;
  detail?: any;
}

export const {} = geologyLongitudeSlice.actions;
