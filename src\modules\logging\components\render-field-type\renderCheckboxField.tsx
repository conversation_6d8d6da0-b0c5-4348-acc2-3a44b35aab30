import { Control, UseFormGetValues, UseFormSetValue } from "react-hook-form";

import { DataEntryBody } from "../../api/data-entry.api";

import { ToogleCommon } from "../../../../components/common/toogle-common";
interface RenderCheckboxFieldProps {
  field: any;
  control: Control<DataEntryBody>;
  index: number;
  setValue: UseFormSetValue<DataEntryBody>;
  getValues: UseFormGetValues<DataEntryBody>;
}

export default function RenderCheckboxField({
  field,
  control,
  index,
  setValue,
  getValues,
}: RenderCheckboxFieldProps) {
  return (
    <>
      <ToogleCommon
        control={control}
        name={`dataEntryValues.${index}.checkBoxValue`}
        onChange={(value) => {
          const currentValues = getValues("dataEntryValues") || [];
          const newValues = [...currentValues];
          newValues[index] = {
            checkBoxValue: value,
            geologysuiteFieldId: field.id,
            fieldType: field?.geologyField?.type,
          };
          const filteredValues = newValues.filter(
            (value) => value && Object.keys(value).length > 0,
          );
          setValue("dataEntryValues", filteredValues);
        }}
      />
    </>
  );
}
