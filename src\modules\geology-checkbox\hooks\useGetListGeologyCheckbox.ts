import { useState } from "react";

import geologyCheckboxRequest from "../api/geology-checkbox.api";
import { GeologyCheckboxQuery } from "../interface/geology-checkbox.query";

export const useGetListGeologyCheckbox = () => {
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any[]>([]);
  const request = async (
    params: GeologyCheckboxQuery,
    onSuccess?: Function,
    onError?: Function,
  ) => {
    setLoading(true);
    const response = await geologyCheckboxRequest.getList({
      ...params,
      isActive: params.isActive ?? true,
    });
    if (response?.state === "success") {
      setData(response.data?.items);
      setTotal(response.data?.pagination?.total);
      setLoading(false);
      onSuccess?.(response.data);
      return response.data;
    } else {
      onError?.(response);
      setLoading(false);
      return null;
    }
  };

  return { request, loading, data, total };
};
