import { createSlice } from "@reduxjs/toolkit";

import { RequestState } from "@/common/configs/app.contants";

import { getDetailGeologyCheckbox, getListGeologyCheckbox } from "./thunks";

const initialState: GeologyCheckboxSliceState = {
  status: RequestState.idle,
  getDetailStatus: RequestState.idle,
};

export const geologyCheckboxSlice = createSlice({
  name: "geologyCheckbox",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getListGeologyCheckbox.pending, (state) => {
        state.status = RequestState.pending;
      })
      .addCase(getListGeologyCheckbox.fulfilled, (state, action) => {
        state.status = RequestState.success;
        state.result = action.payload.data;
      })
      .addCase(getDetailGeologyCheckbox.pending, (state) => {
        state.getDetailStatus = RequestState.pending;
      })
      .addCase(getDetailGeologyCheckbox.fulfilled, (state, action) => {
        state.getDetailStatus = RequestState.success;
        state.detail = action.payload;
      });
  },
});

export interface GeologyCheckboxSliceState {
  result?: any;
  status: RequestState;
  getDetailStatus: RequestState;
  detail?: any;
}

export const {} = geologyCheckboxSlice.actions;
