import { Control, UseFormGetValues, UseFormSetValue } from "react-hook-form";

import { FieldType } from "@/modules/geology-suite-field/const/enum";

import { DataEntryBody } from "../api/data-entry.api";
import RenderDescription from "./render-field-type/render-description";
import RenderRock<PERSON><PERSON>Field from "./render-field-type/render-rocktree-field";
import Render<PERSON><PERSON>ckboxField from "./render-field-type/renderCheckboxField";
import RenderColourField from "./render-field-type/renderColourField";
import RenderDateField from "./render-field-type/renderDateField";
import RenderLatitudeField from "./render-field-type/renderLatitudeField";
import RenderNumberField from "./render-field-type/renderNumberField";
import { RenderPickList } from "./render-field-type/renderPickList";
import { RenderRockGroup } from "./render-field-type/renderRockGroup";
import RenderRockSelect from "./render-field-type/renderRockSelect";
import RenderRockType from "./render-field-type/renderRockType";
import RenderUrl<PERSON>ield from "./render-field-type/renderUrlField";

export interface IRenderFieldProps {
  field: any;
  control: Control<DataEntryBody>;
  setValue: UseFormSetValue<DataEntryBody>;
  getValues: UseFormGetValues<DataEntryBody>;
  index: number;
}

export function RenderField({
  field,
  control,
  setValue,
  getValues,
  index,
}: IRenderFieldProps) {
  switch (field?.geologyField?.type) {
    case FieldType.Colour:
      return (
        <RenderColourField
          field={field}
          control={control}
          index={index}
          setValue={setValue}
          getValues={getValues}
        />
      );
    case FieldType.NumberField:
      return (
        <RenderNumberField
          field={field}
          control={control}
          index={index}
          setValue={setValue}
          getValues={getValues}
        />
      );
    case FieldType.RockGroup:
      return (
        <RenderRockGroup
          field={field}
          control={control}
          index={index}
          setValue={setValue}
          getValues={getValues}
        />
      );
    case FieldType.PickList:
      return (
        <RenderPickList
          field={field}
          control={control}
          index={index}
          setValue={setValue}
          getValues={getValues}
        />
      );
    case FieldType.Description:
      return (
        <RenderDescription
          field={field}
          index={index}
          control={control}
          setValue={setValue}
          getValues={getValues}
        />
      );
    case FieldType.RockType:
      return (
        <RenderRockType
          field={field}
          control={control}
          index={index}
          setValue={setValue}
          getValues={getValues}
        />
      );
    case FieldType.RockSelect:
      return (
        <RenderRockSelect
          field={field}
          index={index}
          control={control}
          setValue={setValue}
          getValues={getValues}
        />
      );
    case FieldType.DateField:
      return (
        <RenderDateField
          _field={field}
          index={index}
          control={control}
          setValue={setValue}
          getValues={getValues}
        />
      );
    case FieldType.RockTree:
      return (
        <RenderRockTreeField _field={field} index={index} control={control} />
      );
    case FieldType.Checkbox:
      return (
        <RenderCheckboxField
          field={field}
          control={control}
          index={index}
          setValue={setValue}
          getValues={getValues}
        />
      );
    case FieldType.Latitude:
    case FieldType.Longitude:
      return (
        <RenderLatitudeField
          field={field}
          control={control}
          index={index}
          setValue={setValue}
          getValues={getValues}
        />
      );
    case FieldType.Url:
      return (
        <RenderUrlField control={control} index={index} setValue={setValue} />
      );
  }
}
