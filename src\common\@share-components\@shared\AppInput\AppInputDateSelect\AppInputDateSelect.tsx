import { DatePicker, DatePickerProps } from "antd";
import dayjs from "dayjs";
import { Controller } from "react-hook-form";

interface AppInputDateSelectProps extends Omit<DatePickerProps, "name"> {
  control: any;

  name: string;
}

const AppInputDateSelect = ({ control, name }: AppInputDateSelectProps) => {
  return (
    <Controller
      control={control}
      name={name}
      render={({ field }) => (
        <DatePicker
          {...field}
          value={field.value ? dayjs(field.value) : null}
          onChange={(date) => {
            if (date) {
              field.onChange(date ? date.format("YYYY-MM-DD") : null);
            } else {
              field.onChange(undefined);
            }
          }}
        />
      )}
    />
  );
};

export default AppInputDateSelect;
