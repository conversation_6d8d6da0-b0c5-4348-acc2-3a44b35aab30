import { Select } from "antd";
import React, { memo, useCallback, useState } from "react";
import { Control, Controller } from "react-hook-form";

import { ErrorTooltip } from "./error-tooltip";

interface RockGroupOption {
  id: number;
  name: string;
  code?: string;
}

interface FieldRockGroupProps {
  control: Control<any>;
  name: string;
  disabled?: boolean;
  mandatory?: boolean;
  options?: RockGroupOption[];
  onKeyDown?: (event: React.KeyboardEvent) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  className?: string;
  id?: string;
  onFieldChange?: (rowIndex: number, fieldPath: string, value: any) => void;
  rowIndex?: number;
  fieldPath?: string;
}

export const FieldRockGroup = memo<FieldRockGroupProps>(
  ({
    control,
    name,
    disabled = false,
    mandatory = false,
    options = [],
    onKeyDown,
    onFocus,
    onBlur,
    className,
    id,
    onFieldChange,
    rowIndex,
    fieldPath,
  }) => {
    const [searchValue, setSearchValue] = useState("");
    const [dropdownOpen, setDropdownOpen] = useState(false);
    const handleKeyDown = useCallback(
      (event: React.KeyboardEvent) => {
        const arrowKeys = [
          "ArrowUp",
          "ArrowDown",
          "ArrowLeft",
          "ArrowRight",
          "Tab",
        ];

        if (arrowKeys.includes(event.key)) {
          if (event.shiftKey) {
            setDropdownOpen(true);
          } else {
            setDropdownOpen(false);
            event.preventDefault();
          }

          onKeyDown?.(event);
        }
      },
      [onKeyDown],
    );

    const handleFocus = useCallback(() => {
      onFocus?.();
    }, [onFocus]);

    const handleBlur = useCallback(() => {
      onBlur?.();
    }, [onBlur]);

    // Create sorted options that prioritize entries beginning with search input
    const createSortedOptions = useCallback(
      (searchValue: string = "") => {
        const inputLower = searchValue.toLowerCase();

        return options
          .filter((option) => {
            const nameLower = option.name?.toLowerCase() || "";
            const codeLower = option.code?.toLowerCase() || "";
            return (
              nameLower.includes(inputLower) || codeLower.includes(inputLower)
            );
          })
          .map((option) => ({
            label: option.name,
            value: option.id,
            originalOption: option,
          }))
          .sort((a, b) => {
            const nameA = a.originalOption.name?.toLowerCase() || "";
            const codeA = a.originalOption.code?.toLowerCase() || "";
            const nameB = b.originalOption.name?.toLowerCase() || "";
            const codeB = b.originalOption.code?.toLowerCase() || "";

            // Check if entries start with the search input
            const aStartsWith =
              nameA.startsWith(inputLower) || codeA.startsWith(inputLower);
            const bStartsWith =
              nameB.startsWith(inputLower) || codeB.startsWith(inputLower);

            // Prioritize entries that start with the search input
            if (aStartsWith && !bStartsWith) {
              return -1;
            }
            if (!aStartsWith && bStartsWith) {
              return 1;
            }

            // If both start with or both don't start with, sort alphabetically
            return nameA.localeCompare(nameB) || codeA.localeCompare(codeB);
          });
      },
      [options],
    );

    const selectOptions = createSortedOptions(searchValue);

    return (
      <Controller
        name={name}
        control={control}
        rules={{
          required: mandatory ? "This field is required" : false,
        }}
        render={({ field, fieldState: { error } }) => (
          <div className="w-full">
            <Select
              {...field}
              id={id}
              disabled={disabled}
              placeholder="Select rock group"
              className={`w-full ${className || ""}`}
              options={selectOptions}
              allowClear
              open={dropdownOpen}
              onDropdownVisibleChange={(open) => {
                setDropdownOpen(open);
              }}
              showSearch
              virtual={selectOptions.length > 100}
              onSearch={(value) => {
                setSearchValue(value);
              }}
              filterOption={false}
              onKeyDown={handleKeyDown}
              onFocus={handleFocus}
              onBlur={handleBlur}
              value={field.value || undefined}
              onChange={(value) => {
                // Ensure we handle undefined/null values properly when clearing
                const finalValue =
                  value === undefined || value === null ? null : value;
                field.onChange(finalValue);

                // Trigger row status update
                if (
                  onFieldChange &&
                  typeof rowIndex === "number" &&
                  fieldPath
                ) {
                  onFieldChange(rowIndex, fieldPath, finalValue);
                }
              }}
              onClear={() => {
                // Explicitly handle clear action
                field.onChange(null);
                setSearchValue(""); // Reset search value when clearing
                if (
                  onFieldChange &&
                  typeof rowIndex === "number" &&
                  fieldPath
                ) {
                  onFieldChange(rowIndex, fieldPath, null);
                }
              }}
            />
            <ErrorTooltip error={error} />
          </div>
        )}
      />
    );
  },
);

FieldRockGroup.displayName = "FieldRockGroup";
