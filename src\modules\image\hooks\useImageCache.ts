import { IDBPDatabase, openDB } from "idb";
import { ImageViewQuery } from "../interface/image.interface";

interface CacheEntry {
  data: any;
  timestamp: number;
}

const DB_NAME = "drillhole-image-cache";
const STORE_NAME = "image-responses";
const CACHE_EXPIRY = 3 * 60 * 1000; // 3 minutes in milliseconds

export const useImageCache = () => {
  let db: IDBPDatabase | null = null;

  const initDB = async (): Promise<IDBPDatabase> => {
    if (db) {
      return db;
    }

    try {
      db = await openDB(DB_NAME, 1, {
        upgrade(database) {
          if (!database.objectStoreNames.contains(STORE_NAME)) {
            database.createObjectStore(STORE_NAME);
          }
        },
      });
      return db;
    } catch {
      throw new Error("Failed to initialize IndexedDB");
    }
  };

  const generateCacheKey = (params: Partial<ImageViewQuery>): string => {
    const sortedImageFilter = (params?.imageFilter ?? [])?.sort((a, b) => {
      if (a.imageTypeId !== b.imageTypeId) {
        return a.imageTypeId - b.imageTypeId;
      }
      return (a.imageSubtypeId ?? 0) - (b.imageSubtypeId ?? 0);
    });
    const keyParams = {
      drillHoleNames: params.drillHoleNames?.join(",") || "",
      imageCategory: params.imageCategory || "",
      imageFilter: sortedImageFilter?.join(",") || "",
      holeIds: params.holeIds?.join(",") || "",
    };
    return JSON.stringify(keyParams);
  };

  const getCachedData = async (
    params: Partial<ImageViewQuery>,
  ): Promise<any | null> => {
    try {
      const database = await initDB();
      const key = generateCacheKey(params);
      const entry = (await database.get(STORE_NAME, key)) as
        | CacheEntry
        | undefined;

      if (!entry) {
        return null;
      }

      const now = Date.now();
      if (now - entry.timestamp > CACHE_EXPIRY) {
        // Cache expired, remove it
        await database.delete(STORE_NAME, key);
      }

      return entry.data;
    } catch {
      throw new Error("Error retrieving from cache");
    }
  };

  const setCachedData = async (
    params: Partial<ImageViewQuery>,
    data: any,
  ): Promise<void> => {
    try {
      const database = await initDB();
      const key = generateCacheKey(params);
      const entry: CacheEntry = {
        data,
        timestamp: Date.now(),
      };
      await database.put(STORE_NAME, entry, key);
    } catch {
      throw new Error("Error storing in cache");
    }
  };

  const clearExpiredCache = async (): Promise<void> => {
    try {
      const database = await initDB();
      const now = Date.now();
      const allKeys = await database.getAllKeys(STORE_NAME);

      for (const key of allKeys) {
        const entry = (await database.get(STORE_NAME, key)) as CacheEntry;
        if (now - entry.timestamp > CACHE_EXPIRY) {
          await database.delete(STORE_NAME, key);
        }
      }
    } catch {
      throw new Error("Error clearing expired cache");
    }
  };

  const clearAllCache = async (): Promise<void> => {
    try {
      const database = await initDB();
      const allKeys = await database.getAllKeys(STORE_NAME);

      // Delete all entries regardless of expiration status
      for (const key of allKeys) {
        await database.delete(STORE_NAME, key);
      }
    } catch {}
  };

  return {
    getCachedData,
    setCachedData,
    clearExpiredCache,
    clearAllCache,
  };
};
