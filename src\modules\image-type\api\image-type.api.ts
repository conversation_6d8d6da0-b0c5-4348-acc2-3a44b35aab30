import { RequestState } from "@/common/configs/app.contants";
import { appRequest } from "@/common/configs/app.di-container";
import { getErrorMessage } from "@/utils/error.utils";

import { ImageTypeQuery } from "../interface/image-type.query";
import { ImageTypeBodyType } from "../model/schema/image-type.schema";

const imageTypeRequest = {
  getList: async (params: ImageTypeQuery) => {
    try {
      const response = await appRequest.get<any>(
        `/services/app/ImageType/GetAll`,
        params,
      );
      return {
        state: RequestState.success,
        data: {
          items: response?.result?.items,
          pagination: {
            current:
              Math.floor(
                (params?.skipCount ?? 1) / (params?.maxResultCount ?? 10),
              ) + 1,
            pageSize: params?.maxResultCount ?? 10,
            total: response?.result?.totalCount,
          },
        },
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  getDetail: async (id: string) => {
    try {
      const response = await appRequest.get<any>(
        `/services/app/ImageType/Get?Id=${id}`,
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },

  create: async (body: ImageTypeBodyType) => {
    try {
      const response = await appRequest.post<any>(
        `/services/app/ImageType/Create`,
        body,
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },

  delete: async (params: { id: string }) => {
    try {
      const response = await appRequest.delete<any>(
        `/services/app/ImageType/Delete?Id=${params.id}`,
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },

  update: async (body: ImageTypeBodyType) => {
    try {
      const response = await appRequest.put<any>(
        `/services/app/ImageType/Update`,
        body,
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
};

export default imageTypeRequest;
