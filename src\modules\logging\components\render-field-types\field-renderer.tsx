import React, { memo } from "react";
import { Control } from "react-hook-form";

import { FieldType } from "@/modules/geology-suite-field/const/enum";

import {
  FieldCheckbox,
  FieldColour,
  FieldDate,
  FieldDepth,
  FieldDescription,
  FieldLatitude,
  FieldLongitude,
  FieldNumber,
  FieldPicklist,
  FieldRockGroup,
  FieldRockSelect,
  FieldRockTree,
  FieldRockType,
  FieldUrl,
} from "./index";

interface LoggingFormData {
  rows: any[];
}

interface FieldRendererProps {
  control: Control<LoggingFormData>;
  fieldType: FieldType | "DEPTH";
  fieldName: string;
  fieldConfig: any;
  onKeyDown?: (event: React.KeyboardEvent) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  disabled?: boolean;
  onFieldChange?: (rowIndex: number, fieldPath: string, value: any) => void;
  rowIndex?: number;
  fieldPath?: string;
}

// Field renderer component
export const FieldRenderer = memo<FieldRendererProps>(
  ({
    control,
    fieldType,
    fieldName,
    fieldConfig,
    onKeyDown,
    onFocus,
    onBlur,
    disabled = false,
    onFieldChange,
    rowIndex,
    fieldPath,
  }) => {
    const commonProps = {
      control,
      name: fieldName,
      disabled,
      mandatory: fieldConfig.isMandatory,
      onKeyDown,
      onFocus,
      onBlur,
      id: `${fieldConfig.valueId}-${fieldType}`,
      onFieldChange,
      rowIndex,
      fieldPath,
    };

    switch (fieldType) {
      case "DEPTH": // Special case for depth fields
        return <FieldDepth {...commonProps} />;
      case FieldType.Colour:
        return <FieldColour {...commonProps} options={fieldConfig.options} />;
      case FieldType.NumberField:
        return <FieldNumber {...commonProps} unit={fieldConfig.unit} />;
      case FieldType.RockGroup:
        return (
          <FieldRockGroup {...commonProps} options={fieldConfig.options} />
        );
      case FieldType.PickList:
        return <FieldPicklist {...commonProps} options={fieldConfig.options} />;
      case FieldType.Description:
        return <FieldDescription {...commonProps} />;
      case FieldType.RockType:
        return (
          <FieldRockType
            {...commonProps}
            options={fieldConfig.options}
            showNumberInput={true}
            numberFieldName={fieldName.replace(".rockTypeId", ".numberValue")}
            unit={fieldConfig.unit}
          />
        );
      case FieldType.RockSelect:
        return (
          <FieldRockSelect
            {...commonProps}
            options={fieldConfig.options}
            showNumberInput={true}
            numberFieldName={fieldName.replace(".rockTypeId", ".numberValue")}
            unit={fieldConfig.unit}
          />
        );
      case FieldType.DateField:
        return <FieldDate {...commonProps} />;
      case FieldType.RockTree:
        return (
          <FieldRockTree {...commonProps} treeData={fieldConfig.treeData} />
        );
      case FieldType.Checkbox:
        return <FieldCheckbox {...commonProps} />;
      case FieldType.Latitude:
        return <FieldLatitude {...commonProps} />;
      case FieldType.Longitude:
        return <FieldLongitude {...commonProps} />;
      case FieldType.Url:
        return <FieldUrl {...commonProps} />;
      default:
        return null;
    }
  },
);

FieldRenderer.displayName = "FieldRenderer";
