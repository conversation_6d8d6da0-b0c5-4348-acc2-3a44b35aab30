import { Empty, Spin } from "antd";
import React from "react";

import { useInfiniteScrollEntries } from "../hooks/useInfiniteScrollEntries";
import { LoggingDesc } from "./logging-des";

interface InfiniteScrollEntriesProps {
  geologySuiteId?: number;
  drillholeId?: number;
  setModalState: (state: any) => void;
  className?: string;
}

export const InfiniteScrollEntries: React.FC<InfiniteScrollEntriesProps> = ({
  geologySuiteId,
  drillholeId,
  setModalState,
  className = "max-h-full overflow-hidden scrollbar-thin bg-white rounded-lg border gap-2",
}) => {
  const {
    data: allLoggingInfos,
    isLoading,
    isInitialLoading,
    hasMore,
    handleScroll,
    scrollContainerRef,
    totalItems,
  } = useInfiniteScrollEntries({
    geologySuiteId,
    drillholeId,
    initialPageSize: 15,
    pageSize: 15,
  });

  return (
    <div className={className}>
      {/* Header */}
      <div className="sticky top-0 bg-white z-10 border-b">
        <div className="relative h-8 flex justify-center items-center">
          <div className="flex items-center">
            <span className="mr-1">Entries</span>
            {totalItems > 0 && (
              <span className="text-xs text-gray-500">
                ({totalItems} entries)
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Content */}
      <div
        ref={scrollContainerRef}
        onScroll={handleScroll}
        className="flex flex-col gap-2 p-2 max-h-[calc(100%-2rem)] overflow-y-auto"
      >
        {isInitialLoading ? (
          // Initial loading state
          <div className="flex justify-center items-center py-8">
            <Spin size="large" />
          </div>
        ) : allLoggingInfos.length === 0 ? (
          // Empty state
          <Empty description="No entries found" className="py-8" />
        ) : (
          <>
            {/* Entries list */}
            {[...allLoggingInfos].map((info, index) => {
              const indexReverse = allLoggingInfos.length - index - 1;
              return (
                <LoggingDesc
                  key={`${info.id}-${index}`}
                  index={indexReverse}
                  dataEntry={info}
                  setModalState={setModalState}
                />
              );
            })}

            {/* Loading more indicator */}
            {isLoading && !isInitialLoading && (
              <div className="flex justify-center items-center py-4">
                <Spin />
                <span className="ml-2 text-sm text-gray-500">
                  Loading more...
                </span>
              </div>
            )}

            {/* End of list indicator */}
            {!hasMore && allLoggingInfos.length > 0 && (
              <div className="flex justify-center items-center py-4">
                <span className="text-sm text-gray-500">
                  All entries loaded ({allLoggingInfos.length} total)
                </span>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};
