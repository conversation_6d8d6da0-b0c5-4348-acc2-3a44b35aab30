import z from "zod";

export const MobileProfileBody = z
  .object({
    id: z.any().optional(),
    name: z.string().trim().min(1, { message: "Required" }),
    description: z.string().trim().optional(),
    isActive: z.boolean(),
    isStandard: z.boolean().optional(),
    isWet: z.boolean().optional(),
    isDry: z.boolean().optional(),
    isUv: z.boolean().optional(),
    isRig: z.boolean().optional(),
    mobileCameraType: z.number().optional().nullable(),
    externalCameraType: z.number().optional().nullable(),
    isDepthIncrement: z.boolean().optional().nullable(),
    depthIncrement: z.number().optional().nullable(),
    isApplyDepthIncrement: z.boolean().optional(),
    rotateImgMobile: z.number().optional().nullable(),
    rotateImgExternal: z.number().optional().nullable(),
    projectIds: z.array(z.number()).optional(),
  })
  .refine(
    (data) => {
      // Phải bật ít nhất 1 trong 2 "Standard" và "Rig"
      return data.isStandard || data.isRig;
    },
    {
      message: "At least one of Standard or Rig must be enabled",
      path: ["isStandard"], // Hiển thị lỗi ở field isStandard
    },
  )
  .refine(
    (data) => {
      // Khi bật "Standard" thì phải bật ít nhất 1 subtype Wet/Dry/UV
      if (data.isStandard) {
        return data.isWet || data.isDry || data.isUv;
      }
      return true;
    },
    {
      message:
        "When Standard is enabled, at least one subtype (Wet/Dry/UV) must be enabled",
      path: ["isWet"], // Hiển thị lỗi ở field isWet
    },
  )
  .refine(
    (data) => {
      // Khi cả Standard và Rig đều tắt thì không được phép
      if (!data.isStandard && !data.isRig) {
        return false;
      }
      return true;
    },
    {
      message: "Cannot disable both Standard and Rig at the same time",
      path: ["isRig"], // Hiển thị lỗi ở field isRig
    },
  );

export type MobileProfileBodyType = z.TypeOf<typeof MobileProfileBody>;
