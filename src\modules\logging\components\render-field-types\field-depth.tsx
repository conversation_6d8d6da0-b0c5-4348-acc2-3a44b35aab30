import { InputNumber } from "antd";
import React, { memo, useCallback } from "react";
import { Control, Controller } from "react-hook-form";

import { ErrorTooltip } from "./error-tooltip";

interface FieldDepthProps {
  control: Control<any>;
  name: string;
  disabled?: boolean;
  onKeyDown?: (event: React.KeyboardEvent) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  placeholder?: string;
  className?: string;
  id?: string;
  onFieldChange?: (rowIndex: number, fieldPath: string, value: any) => void;
  rowIndex?: number;
  fieldPath?: string;
}

export const FieldDepth = memo<FieldDepthProps>(
  ({
    control,
    name,
    disabled = false,
    onKeyDown,
    onFocus,
    onBlur,
    placeholder,
    className,
    id,
    onFieldChange,
    rowIndex,
    fieldPath,
  }) => {
    const handleKeyDown = useCallback(
      (event: React.KeyboardEvent) => {
        // Handle tab navigation and other keys
        onKeyDown?.(event);
      },
      [onKeyDown],
    );

    const handleFocus = useCallback(() => {
      onFocus?.();
    }, [onFocus]);

    const handleBlur = useCallback(() => {
      onBlur?.();
    }, [onBlur]);

    return (
      <Controller
        name={name}
        control={control}
        render={({ field, fieldState: { error } }) => (
          <div className="w-full">
            <div className="w-full h-full flex items-center">
              <InputNumber
                {...field}
                id={id}
                disabled={disabled}
                decimalSeparator="."
                placeholder={placeholder}
                className={`w-full min-h-[36px] ${className || ""}`}
                onKeyDown={handleKeyDown}
                onFocus={handleFocus}
                onBlur={() => {
                  // Apply truncation to 2 decimal places when user finishes editing (onBlur)
                  const currentValue = field.value;
                  if (currentValue !== null && currentValue !== undefined) {
                    // 1e-8 to handle currentValue = 4.1
                    const truncatedValue =
                      Math.floor(currentValue * 100 + 1e-8) / 100;

                    // Only update if the truncated value is different to avoid unnecessary re-renders
                    if (truncatedValue !== currentValue) {
                      field.onChange(truncatedValue);

                      // Trigger row status update with truncated value
                      if (
                        onFieldChange &&
                        typeof rowIndex === "number" &&
                        fieldPath
                      ) {
                        onFieldChange(rowIndex, fieldPath, truncatedValue);
                      }
                    }
                  }

                  // Call the original onBlur handler
                  handleBlur();
                }}
                onChange={(value) => {
                  // Update field value without rounding to allow complete typing
                  field.onChange(value);

                  // Trigger row status update for immediate feedback (without rounding)
                  // This ensures the focus preservation system works correctly
                  if (
                    onFieldChange &&
                    typeof rowIndex === "number" &&
                    fieldPath
                  ) {
                    onFieldChange(rowIndex, fieldPath, value);
                  }
                }}
              />
            </div>
            <ErrorTooltip error={error} />
          </div>
        )}
      />
    );
  },
);

FieldDepth.displayName = "FieldDepth";
