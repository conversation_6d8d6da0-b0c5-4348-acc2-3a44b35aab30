import imageRequest from "../api/image.api";
import { ImageViewQuery } from "../interface/image.interface";

export const useGetImageByView = () => {
  async function request(
    params: ImageViewQuery, // Renamed to rawParams to indicate potential pre-transformation state
    setLoading?: Function,
    onSuccess?: Function,
    onError?: Function,
  ) {
    setLoading?.(true);
    // Ensure all parameter keys are in camelCase before sending to the API
    const response = await imageRequest.getAllByView(params);
    if (response?.status === 200) {
      onSuccess?.(response.data?.result);
      setLoading?.(false);
      return response.data?.result ?? [];
    } else {
      onError?.(response);
      setLoading?.(false);
    }
  }

  return { request };
};
