import { But<PERSON>, Select } from "antd";
import { useState } from "react";
import { UseFormReturn } from "react-hook-form";

import { useAppSelector } from "@/common/vendors/redux/store/hook";

export const RockGroupFill = ({
  form,
  setIsModalOpen,
  selectedColumn,
}: {
  form: UseFormReturn<any>;
  setIsModalOpen: (isModalOpen: boolean) => void;
  selectedColumn: string;
}) => {
  const geologySuite = useAppSelector((state) => state.importData.geologySuite);
  const geologySuiteField = geologySuite?.geologySuiteFields.find(
    (field) => field.name === selectedColumn,
  );
  const options = geologySuiteField?.geologyField?.rockGroup?.rockTypes?.map(
    (rockType) => ({
      label: rockType?.name,
      value: rockType?.name,
    }),
  );
  const [fillValue, setFillValue] = useState("");
  const handleFillColumn = (keyName: string, value: any) => {
    form.watch("dataUpload").forEach((d: any, index: number) => {
      if (d[keyName] === "") {
        form.setValue(`dataUpload.${index}.${keyName}`, value);
      }
    });
    setIsModalOpen(false);
  };
  return (
    <div className="flex flex-col gap-4">
      <Select
        options={options}
        value={fillValue}
        onChange={(value) => setFillValue(value)}
        showSearch
        allowClear
        filterOption={true}
        optionFilterProp="label"
        placeholder="Select Rock Group"
        className="w-full"
      />
      <Button onClick={() => handleFillColumn(selectedColumn, fillValue)}>
        Fill Rock Group
      </Button>
    </div>
  );
};
