import { useState } from "react";

import imageTypeRequest from "../api/image-type.api";

export const useGetDetailImageType = () => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any>();
  const request = async (
    id: string,
    onSuccess?: Function,
    onError?: Function,
  ) => {
    setLoading(true);
    const response = await imageTypeRequest.getDetail(id);
    setData(response.data);
    if (response?.state === "success") {
      setData(response.data);
      setLoading(false);
      onSuccess?.(response.data);
      return response.data;
    } else {
      onError?.(response);
      setLoading(false);
      return null;
    }
  };

  return { request, loading, data };
};
