"use client";
import { Tabs, TabsProps } from "antd";

import PermissionProvider from "@/components/layout/permission-provider";
import { PERMISSIONS } from "@/constants/general.const";
import MergeDrillhole from "@/modules/merge-drillhole/components/merge-drillhole";

export default function Page() {
  const items: TabsProps["items"] = [
    {
      key: "1",
      label: "Merge Drillhole",
      children: <MergeDrillhole />,
    },
  ];
  return (
    <PermissionProvider
      permissions={[
        PERMISSIONS.Admin,
        PERMISSIONS.Company,
        PERMISSIONS.EditorUser,
        PERMISSIONS.ViewerUser,
      ]}
    >
      <Tabs items={items} />
    </PermissionProvider>
  );
}
