"use client";

import PermissionProvider from "@/components/layout/permission-provider";
import { PERMISSIONS } from "@/constants/general.const";
import { RockTypesTree } from "@/modules/rock-types-tree";

export default function RockTypesTreePage() {
  return (
    <PermissionProvider
      permissions={[
        PERMISSIONS.Admin,
        PERMISSIONS.Company,
        PERMISSIONS.EditorUser,
      ]}
    >
      <RockTypesTree />
    </PermissionProvider>
  );
}
