import { createAppAsyncThunk } from "@/common/vendors/redux/store/createAppAsyncThunk";

import geologyLongitudeRequest from "../api/geology-longitude.api";
import { GeologyLongitudeQuery } from "../interface/geology-longitude.query";

export const getListGeologyLongitude = createAppAsyncThunk(
  "geologyLongitude/list",
  async (query: GeologyLongitudeQuery) => {
    const { page = 1, pageSize = 10, ...otherQueries } = query;
    const response = await geologyLongitudeRequest.getList({
      skipCount: (page - 1) * pageSize,
      maxResultCount: pageSize,
      ...otherQueries,
    });

    return response;
  },
);

export const getDetailGeologyLongitude = createAppAsyncThunk(
  "geologyLongitude/detail",
  async (id: string) => {
    const response = await geologyLongitudeRequest.getDetail(id);
    return response.data;
  },
);
