/* eslint-disable react-hooks/exhaustive-deps */
"use client";
import { DeleteOutlined, EditOutlined, PlusOutlined } from "@ant-design/icons";
import { <PERSON>readcrumb, Divider, TableColumnsType } from "antd";
import Link from "next/link";
import { useParams, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

import { useAppSelector } from "@/common/vendors/redux/store/hook";
import { TableCommon } from "@/components/common/table-common";
import { DATA_TYPE, PROCESS_TYPE } from "@/constants/general.const";
import { useGetListPolygon } from "@/modules/polygon/hooks/useGetListPolygon.hook";

import { useGetDetailWorkflow } from "../hooks/useGetDetailWorkflow.hook";
import { useGetListStepWorkflow } from "../hooks/useGetListStepWorkflow.hook";
import { ModalDetailStep } from "./modal-detailstep";
export function getLabelByValue(value: number, ENUM: any) {
  for (const key in ENUM) {
    if (ENUM[key].value === value) {
      return ENUM[key].label;
    }
  }
  return null;
}
export function WorkflowDetail() {
  const params = useParams();
  const id = params.id[0];
  const [workflowDetail, setWorkflowDetail] = useState<any>({});
  const { request: requestGetDetailWorkflow } = useGetDetailWorkflow();
  const [requestGetListPolygon] = useGetListPolygon();
  const [polygons, setPolygons] = useState<any[]>([]);
  const searchParams = useSearchParams();
  //get all query params
  let querySearch: any;
  searchParams.forEach((value, key) => {
    querySearch = {
      ...querySearch,
      [key]: value,
    };
  });

  const { data, request: requestGetListStepWorkflow } =
    useGetListStepWorkflow();
  const fetchListStepWorkflow = (
    params = {
      keyword: querySearch?.Keyword,
      skipCount: querySearch?.skipCount,
      maxResultCount: querySearch?.maxResultCount,
      WorkFlowId: id as any,
    },
  ) => {
    requestGetListStepWorkflow(params);
  };
  useEffect(() => {
    fetchListStepWorkflow();
    requestGetListPolygon(
      {},
      () => {},
      (res: any) => {
        setPolygons(res?.items);
      },
      () => {},
    );
  }, []);
  useEffect(() => {
    requestGetDetailWorkflow({ id }, (res: any) => {
      setWorkflowDetail(res);
    });
  }, []);

  const [modalState, setModalState] = useState<any>({
    isOpen: false,
    type: "create",
  });

  const column: TableColumnsType<any> = [
    {
      title: "Step",
      dataIndex: "index",
      key: "index",
      render(value, record, index) {
        return <p key={index}>{index + 1}</p>;
      },
    },
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "Process Type",
      dataIndex: "sourceType",
      key: "sourceType",
      render(value, record, index) {
        return (
          <p key={index}>{getLabelByValue(record.processType, PROCESS_TYPE)}</p>
        );
      },
    },
    {
      title: "Source Data",
      dataIndex: "sourceDataName",
      key: "sourceDataName",
      render(value, record, index) {
        return (
          <p key={index}>{getLabelByValue(record.dataSourceType, DATA_TYPE)}</p>
        );
      },
    },

    {
      title: "Action",
      key: "action",
      render: (_, record, index) => {
        return (
          <div className="flex gap-3" key={index}>
            <EditOutlined
              onClick={() =>
                setModalState({
                  ...modalState,
                  isOpen: true,
                  type: "update",
                  detailInfo: record,
                })
              }
              style={{ fontSize: 16 }}
              className="hover:text-primary cursor-pointer"
            />
            <DeleteOutlined
              onClick={() =>
                setModalState({
                  ...modalState,
                  isOpen: true,
                  type: "delete",
                  detailInfo: record,
                })
              }
              className="hover:text-primary cursor-pointer"
              style={{ fontSize: 16 }}
            />
          </div>
        );
      },
    },
  ];
  const projectId = useAppSelector((state) => state.user.userInfo.projectId);
  const prospectId = useAppSelector((state) => state.user.userInfo.prospectId);
  return (
    <div className="flex flex-col gap-3">
      {modalState.isOpen && (
        <ModalDetailStep
          modalState={modalState}
          setModalState={setModalState}
          fetchListStepWorkflow={fetchListStepWorkflow}
          listPolygons={polygons}
        />
      )}
      <Breadcrumb
        items={[
          {
            title: (
              <Link
                href={`/workflows-management?projectId=${projectId}&prospectId=${prospectId}&isActive=true`}
                className="text-primary hover:underline"
              >
                Workflow Management
              </Link>
            ),
          },

          {
            title: workflowDetail?.name,
          },
        ]}
      />
      <p className="text-34-34 font-semibold">
        Workflow {workflowDetail?.name} Steps
      </p>
      <Divider />
      <TableCommon
        className="font-visby w-full"
        columns={column}
        dataSource={data as any}
        footer={() => (
          <div className="justify-center my-2 ">
            <button
              onClick={() =>
                setModalState({
                  ...modalState,
                  isOpen: true,
                  type: "create",
                  detailInfo: undefined,
                })
              }
              className="btn w-full bg-primary border-none hover:bg-primary-hover"
            >
              <PlusOutlined style={{ fontSize: "18px", color: "white" }} />
              <span className="font-bold uppercase text-white ">Add Step</span>
            </button>
          </div>
        )}
      />
    </div>
  );
}
