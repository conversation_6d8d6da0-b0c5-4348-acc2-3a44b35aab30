/* eslint-disable complexity */
import { FieldType } from "@/modules/geology-suite-field/const/enum";

export const getGeologyFieldTypeText = (type: number): string => {
  switch (type) {
    case FieldType.Colour:
      return "Color";
    case FieldType.NumberField:
      return "Number";
    case FieldType.RockGroup:
      return "Rock Group";
    case FieldType.PickList:
      return "Pick List";
    case FieldType.Description:
      return "Geology Description";
    case FieldType.RockType:
      return "Rock Type Number";
    case FieldType.RockSelect:
      return "Rock Select Number";
    case FieldType.DateField:
      return "Geology Date";
    case FieldType.RockTree:
      return "Rock Tree";
    case FieldType.Url:
      return "Geology URL";
    case FieldType.Latitude:
      return "Geology Latitude";
    case FieldType.Longitude:
      return "Geology Longitude";
    case FieldType.Checkbox:
      return "Geology Checkbox";

    default:
      return "";
  }
};

export const getGeologyFieldReference = (item: any): string => {
  switch (item.type) {
    case FieldType.NumberField:
      return item.number?.name || "";
    case FieldType.RockGroup:
      return item.rockGroup?.name || "";
    case FieldType.PickList:
      return item.pickList?.name || "";
    case FieldType.Description:
      return item.geologyDescription?.name || "";
    case FieldType.RockType:
      return item.rockTypeNumber?.rockType?.name &&
        item.rockTypeNumber?.number?.name
        ? `${item.rockTypeNumber.rockType.name} - ${item.rockTypeNumber.number.name}`
        : "";
    case FieldType.RockSelect:
      return item.rockSelectNumber?.rockGroup?.name &&
        item.rockSelectNumber?.number?.name
        ? `${item.rockSelectNumber.rockGroup.name} - ${item.rockSelectNumber.number.name}`
        : "";
    case FieldType.DateField:
      return item.geologyDate?.name || "";
    case FieldType.RockTree:
      return item.rockNode?.name || "";
    case FieldType.Url:
      return item.geologyUrl?.name || "";
    case FieldType.Latitude:
      return item.geologyLatitude?.name || "";
    case FieldType.Longitude:
      return item.geologyLongitude?.name || "";
    case FieldType.Checkbox:
      return item.geologyCheckBox?.name || "";

    default:
      return "";
  }
};

export const getGeologyFieldDisplayInfo = (item: any) => {
  const typeText = getGeologyFieldTypeText(item.type);
  const reference = getGeologyFieldReference(item);
  const lastModificationTime = item.lastModificationTime;

  return {
    typeText,
    reference,
    name: item.name,
    lastModificationTime,
  };
};
