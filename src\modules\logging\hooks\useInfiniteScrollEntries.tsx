import { useCallback, useEffect, useRef, useState } from "react";

import {
  useAppDispatch,
  useAppSelector,
} from "@/common/vendors/redux/store/hook";

import { getLoggingInfoInfinite } from "../redux/loggingSlice/thunks";

interface UseInfiniteScrollEntriesProps {
  geologySuiteId?: number;
  drillholeId?: number;
  initialPageSize?: number;
  pageSize?: number;
}

export const useInfiniteScrollEntries = ({
  geologySuiteId,
  drillholeId,
  initialPageSize = 15,
  pageSize = 15,
}: UseInfiniteScrollEntriesProps) => {
  const dispatch = useAppDispatch();
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Redux state with fallback defaults
  const { allLoggings, infiniteScrollState, getLoggingInfoStatus } =
    useAppSelector((state) => state.logging);

  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize infinite scroll data
  const initializeInfiniteScroll = useCallback(async () => {
    if (!geologySuiteId || !drillholeId || isInitialized) {
      return;
    }

    setIsInitialized(true);
    await dispatch(
      getLoggingInfoInfinite({
        GeologySuiteId: geologySuiteId,
        DrillholeId: drillholeId,
        skipCount: 0,
        maxResultCount: initialPageSize,
        reset: true,
      }),
    );
  }, [dispatch, geologySuiteId, drillholeId, initialPageSize, isInitialized]);

  // Load more data
  const loadMoreData = useCallback(async () => {
    if (!geologySuiteId || !drillholeId) {
      return;
    }
    if (infiniteScrollState?.loading || !infiniteScrollState?.hasMore) {
      return;
    }

    await dispatch(
      getLoggingInfoInfinite({
        GeologySuiteId: geologySuiteId,
        DrillholeId: drillholeId,
        skipCount: (infiniteScrollState?.currentPage + 1) * pageSize,
        maxResultCount: pageSize,
        reset: false,
      }),
    );
  }, [
    dispatch,
    geologySuiteId,
    drillholeId,
    infiniteScrollState?.loading,
    infiniteScrollState?.hasMore,
    infiniteScrollState?.currentPage,
    pageSize,
  ]);

  // Scroll event handler
  const handleScroll = useCallback(
    (event: React.UIEvent<HTMLDivElement>) => {
      const target = event.currentTarget;
      const { scrollTop, scrollHeight, clientHeight } = target;

      // Load more when scrolled to within 100px of bottom
      const threshold = 100;
      const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

      if (
        distanceFromBottom < threshold &&
        infiniteScrollState?.hasMore &&
        !infiniteScrollState?.loading
      ) {
        loadMoreData();
      }
    },
    [loadMoreData, infiniteScrollState?.hasMore, infiniteScrollState?.loading],
  );

  // Reset scroll state when parameters change
  useEffect(() => {
    setIsInitialized(false);
  }, [geologySuiteId, drillholeId]);

  // Initialize on mount or parameter change
  useEffect(() => {
    initializeInfiniteScroll();
  }, [initializeInfiniteScroll]);

  return {
    // Data
    data: allLoggings || [],

    // Loading states
    isLoading: infiniteScrollState?.loading,
    isInitialLoading:
      getLoggingInfoStatus === "pending" &&
      infiniteScrollState?.currentPage === 0,
    hasMore: infiniteScrollState?.hasMore,

    // Scroll handling
    scrollContainerRef,
    handleScroll,

    // Manual controls
    loadMoreData,
    initializeInfiniteScroll,

    // Pagination info
    currentPage: infiniteScrollState?.currentPage,
    totalItems: infiniteScrollState?.totalItems,
    loadedItems: (allLoggings || []).length,
  };
};
