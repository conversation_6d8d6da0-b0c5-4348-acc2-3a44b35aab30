import { useState } from "react";

import geologyUrlRequest from "../api/geology-url.api";

export const useDeleteGeologyUrl = () => {
  const [loading, setLoading] = useState(false);

  async function request(
    params: {
      id: string;
    },
    onSuccess?: Function,
    onError?: Function,
  ) {
    setLoading(true);
    const response = await geologyUrlRequest.delete(params);
    if (response.state === "success") {
      onSuccess?.(response.data);
      setLoading(false);
    } else {
      onError?.(response);
      setLoading(false);
    }
  }
  return {
    request,
    loading,
  };
};
