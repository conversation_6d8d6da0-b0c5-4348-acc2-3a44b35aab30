"use client";
import { useParams } from "next/navigation";

import PermissionProvider from "@/components/layout/permission-provider";
import { PERMISSIONS } from "@/constants/general.const";
import { ProcessBatchImage } from "@/modules/process-batch-image/components/process-batch-image";

export default function Page() {
  const params = useParams();
  return (
    <PermissionProvider
      permissions={[
        PERMISSIONS.Admin,
        PERMISSIONS.Company,
        PERMISSIONS.EditorUser,
        PERMISSIONS.ViewerUser,
      ]}
    >
      <ProcessBatchImage id={params.id[0]} />
    </PermissionProvider>
  );
}
