import { createAppAsyncThunk } from "@/common/vendors/redux/store/createAppAsyncThunk";

import geologyUrlRequest from "../api/geology-url.api";
import { GeologyUrlQuery } from "../interface/geology-url.query";

export const getListGeologyUrl = createAppAsyncThunk(
  "geologyUrl/list",
  async (query: GeologyUrlQuery) => {
    const { page = 1, pageSize = 10, ...otherQueries } = query;
    const response = await geologyUrlRequest.getList({
      skipCount: (page - 1) * pageSize,
      maxResultCount: pageSize,
      ...otherQueries,
    });

    return response;
  },
);

export const getDetailGeologyUrl = createAppAsyncThunk(
  "geologyUrl/detail",
  async (id: string) => {
    const response = await geologyUrlRequest.getDetail(id);
    return response.data;
  },
);
