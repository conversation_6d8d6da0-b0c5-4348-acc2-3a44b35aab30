import { Tooltip } from "antd";
import React, { memo } from "react";

interface ErrorTooltipProps {
  error?: {
    message?: string;
  };
  children?: React.ReactNode;
}

/**
 * ErrorTooltip component that wraps inline validation error messages with a tooltip
 * for better readability while preserving the existing compact display.
 *
 * Features:
 * - Keeps existing inline error message display
 * - Adds hover tooltip with larger, more readable text
 * - Smooth hover transitions with appropriate delays
 * - Proper positioning within table boundaries
 * - Responsive to different error message lengths
 */
export const ErrorTooltip = memo<ErrorTooltipProps>(({ error, children }) => {
  if (!error?.message) {
    return <>{children}</>;
  }

  const errorMessage = error.message;

  return (
    <>
      {children}
      <Tooltip
        title={
          <div
            style={{
              fontSize: "14px",
              lineHeight: "1.4",
              maxWidth: "300px",
              wordWrap: "break-word",
            }}
          >
            {errorMessage}
          </div>
        }
        placement="bottomLeft"
        mouseEnterDelay={0.3}
        mouseLeaveDelay={0.1}
        color="red"
        overlayStyle={{
          zIndex: 1060, // Higher than table elements
        }}
        getPopupContainer={(triggerNode) => {
          // Try to find the table container to ensure tooltip stays within bounds
          const tableContainer =
            triggerNode.closest(".logging-grid") ||
            triggerNode.closest(".ant-table-wrapper") ||
            document.body;
          return tableContainer as HTMLElement;
        }}
      >
        <div
          className="text-red-500 text-xs mt-1 cursor-help hover:text-red-600 transition-colors duration-200"
          style={{
            display: "inline-block",
            maxWidth: "100%",
            wordBreak: "break-word",
            lineHeight: "1.3",
          }}
        >
          {errorMessage}
        </div>
      </Tooltip>
    </>
  );
});

ErrorTooltip.displayName = "ErrorTooltip";
