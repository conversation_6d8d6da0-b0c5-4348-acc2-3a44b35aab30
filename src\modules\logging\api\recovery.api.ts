import { RequestState } from "@/common/configs/app.contants";
import { appRequest } from "@/common/configs/app.di-container";
import { getErrorMessage } from "@/utils/error.utils";

import { RockLineCalculationResult } from "../types/logging.types";

export interface RecoveryResponse {
  items: Array<RockLineCalculationResult>;
  totalCount: number;
}

export interface TrayDepthResponse {
  items: Array<{
    drillHoleId: number;
    trayNumber: number;
    startDepth: number;
    endDepth: number;
    id: number;
  }>;
  totalCount: number;
}

export interface GetRecoveryByDrillHoleParams {
  drillHoleId: number;
  skipCount?: number;
  maxResultCount?: number;
}

interface ApiResponse<T> {
  result: T;
  targetUrl: string | null;
  success: boolean;
  error: any | null;
  unAuthorizedRequest: boolean;
  __abp: boolean;
}

interface CalculateRecoveryBySelectPointParams {
  drillHoleId: number;
  imageCropId: number;
  x: number;
}
export interface JoinRockLineParams {
  rockLineId: number;
}
interface UpdateRockLineParams {
  id: number;
  type?: number;
  depthFrom?: number;
  depthTo?: number;
  startX?: number;
  startY?: number;
  endX: number;
  endY?: number;
  imageCropId?: number;
}

interface CreateRockLineParams {
  type: number;
  depthFrom: number;
  depthTo: number;
  startX: number;
  startY: number;
  endX: number;
  endY: number;
  imageCropId: number;
}

const recoveryRequest = {
  getRecoveryByDrillHole: async (params: GetRecoveryByDrillHoleParams) => {
    try {
      const response = await appRequest.get<ApiResponse<RecoveryResponse>>(
        `/services/app/DrillHole/GetRecoveryByDrillHole`,
        params,
      );
      return {
        state: RequestState.success,
        data: response.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },

  calculateRecovery: async (drillHoleId: number) => {
    try {
      const response = await appRequest.post<ApiResponse<void>>(
        `/services/app/DrillHole/CalculateRecovery`,
        { drillHoleId },
      );
      return {
        state: RequestState.success,
        data: response.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },

  calculateDepthBySelectPoint: async (
    params: CalculateRecoveryBySelectPointParams,
  ) => {
    try {
      const response = await appRequest.post<ApiResponse<number>>(
        `/services/app/DrillHole/CalculateDepthBySelectPoint`,
        params,
      );
      return {
        state: RequestState.success,
        data: response.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },

  updateRockLine: async (params: UpdateRockLineParams) => {
    try {
      const response = await appRequest.put<ApiResponse<any>>(
        `/services/app/RockLine/Update`,
        params,
      );
      return {
        state: RequestState.success,
        data: response.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },

  createRockLine: async (params: CreateRockLineParams) => {
    try {
      const response = await appRequest.post<ApiResponse<number>>(
        `/services/app/RockLine/Create`,
        params,
      );
      return {
        state: RequestState.success,
        data: response.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  joinRockLine: async (params: JoinRockLineParams) => {
    try {
      const response = await appRequest.post<ApiResponse<any>>(
        `/services/app/RockLine/JoinRockLine`,
        params,
      );
      return {
        state: RequestState.success,
        data: response.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  removeJoinRockLine: async (params: JoinRockLineParams) => {
    try {
      const response = await appRequest.put<ApiResponse<any>>(
        `/services/app/RockLine/RemoveJoinRockLine`,
        params,
      );
      return {
        state: RequestState.success,
        data: response.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },

  deleteRockLine: async (id: number) => {
    try {
      const response = await appRequest.delete<ApiResponse<void>>(
        `/services/app/RockLine/Delete?id=${id}`,
      );
      return {
        state: RequestState.success,
        data: response.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },

  exportData: async (params: {
    projectId: number;
    prospectId?: number;
    drillHoleIds: number[];
    exportType: number;
  }) => {
    try {
      const response = await appRequest.post<ApiResponse<void>>(
        `/services/app/LoggingView/ExportData`,
        params,
      );
      return {
        state: RequestState.success,
        data: response.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },

  getTrayDepthResult: async (params: {
    skipCount?: number;
    maxResultCount?: number;
    drillHoleId?: number;
  }) => {
    try {
      const response = await appRequest.get<ApiResponse<TrayDepthResponse>>(
        `/services/app/DrillHole/GetTrayDepthResult`,
        params,
      );
      return {
        state: RequestState.success,
        data: response.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
};

export default recoveryRequest;
