/* eslint-disable @next/next/no-img-element */
import {
  EyeOutlined,
  PictureOutlined,
  PlusOutlined,
  UploadOutlined,
} from "@ant-design/icons";
import { Button, message, Modal, Tabs, Upload } from "antd";
import { memo, useState } from "react";
import { Control, Controller } from "react-hook-form";

import imageRequest from "@/modules/image/api/image.api";
import { ErrorTooltip } from "./error-tooltip";

interface FieldUrlProps {
  control: Control<any>;
  name: string;
  disabled?: boolean;
  mandatory?: boolean;
  onFieldChange?: (rowIndex: number, fieldPath: string, value: any) => void;
  rowIndex?: number;
  fieldPath?: string;
}

export const FieldUrl = memo<FieldUrlProps>(
  ({
    control,
    name,
    disabled = false,
    mandatory = false,

    onFieldChange,
    rowIndex,
    fieldPath,
  }) => {
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [uploadedImage, setUploadedImage] = useState<string | null>(null);
    const [activeTab, setActiveTab] = useState("upload");

    const showModal = () => {
      setIsModalVisible(true);
      setActiveTab("upload");
    };

    const showPreviewModal = () => {
      setIsModalVisible(true);
      setActiveTab("preview");
    };

    const handleOk = (field: any) => {
      if (uploadedImage) {
        // Fill URL vào input field
        if (onFieldChange && typeof rowIndex === "number" && fieldPath) {
          onFieldChange(rowIndex, fieldPath, uploadedImage);
        }
        // Cập nhật field.value của React Hook Form
        field?.onChange?.(uploadedImage);
        message.success("URL filled successfully!");
      }
      setIsModalVisible(false);
    };

    const handleCancel = () => {
      setIsModalVisible(false);
    };

    const handleUpload = async (info: any) => {
      if (info.file.status === "uploading") {
      } else if (info.file.status === "done") {
        try {
          // Upload lên server
          const uploadResult = await imageRequest.uploadImage({
            image: info.file.originFileObj,
          });

          if (uploadResult.state === "success") {
            const imageUrl = uploadResult.data?.result;

            // Chỉ lưu URL để preview, không tự động fill vào input
            setUploadedImage(imageUrl);
            message.success(
              'Image uploaded successfully! Click "Fill URL" to add to input.',
            );
          } else {
            message.error("Upload failed: " + uploadResult.message);
          }
        } catch {
          message.error("Upload failed!");
        }
      } else if (info.file.status === "error") {
        message.error("Upload failed!");
      }
    };

    return (
      <Controller
        name={name}
        control={control}
        rules={{
          required: mandatory ? "This field is required" : false,
        }}
        render={({ field, fieldState: { error } }) => {
          return (
            <div className="w-full">
              <div className="flex items-center gap-2">
                {/* URL Display Area */}
                <div
                  className="flex-1 p-2 border border-gray-300 rounded bg-gray-50 min-h-[32px] flex items-center cursor-pointer hover:bg-gray-100 transition-colors"
                  onClick={field.value ? showPreviewModal : showModal}
                >
                  {field.value ? (
                    <div className="flex items-center gap-2 w-full">
                      <div className="w-6 h-6 rounded overflow-hidden flex-shrink-0">
                        <img
                          src={field.value}
                          alt="Preview"
                          className="w-6 h-6 rounded overflow-hidden flex-shrink-0"
                          style={{ objectFit: "cover" }}
                        />
                      </div>
                      <span className="text-sm text-gray-600 truncate flex-1">
                        {field.value}
                      </span>
                      <EyeOutlined className="text-gray-400 text-xs" />
                    </div>
                  ) : (
                    <div className="flex items-center gap-2 text-gray-400">
                      <PictureOutlined className="text-sm" />
                    </div>
                  )}
                </div>

                {/* Upload Button - only show if no image */}
                {!field.value && (
                  <Button
                    icon={<UploadOutlined />}
                    onClick={showModal}
                    disabled={disabled}
                    type="primary"
                    size="small"
                  >
                    Upload
                  </Button>
                )}
              </div>
              <ErrorTooltip error={error} />

              {/* Unified Modal with Tabs */}
              <Modal
                title={
                  <div className="flex items-center gap-2">
                    {activeTab === "upload" ? (
                      <UploadOutlined className="text-blue-500" />
                    ) : (
                      <PictureOutlined className="text-green-500" />
                    )}
                    <span>
                      {activeTab === "upload"
                        ? "Upload Image"
                        : "Image Preview"}
                    </span>
                  </div>
                }
                open={isModalVisible}
                onOk={() => handleOk(field)}
                onCancel={handleCancel}
                width={800}
                okText={activeTab === "upload" ? "Fill URL" : "Close"}
                cancelText="Cancel"
                okButtonProps={{
                  disabled: activeTab === "upload" && !uploadedImage,
                  className:
                    activeTab === "upload"
                      ? "bg-blue-500 hover:bg-blue-600"
                      : "bg-gray-500 hover:bg-gray-600",
                }}
                footer={
                  activeTab === "preview"
                    ? [
                        <Button
                          key="upload"
                          type="primary"
                          onClick={() => setActiveTab("upload")}
                        >
                          <PlusOutlined /> Upload New
                        </Button>,
                        <Button key="cancel" onClick={handleCancel}>
                          Close
                        </Button>,
                      ]
                    : undefined
                }
              >
                <Tabs
                  activeKey={activeTab}
                  onChange={setActiveTab}
                  items={[
                    {
                      key: "upload",
                      label: (
                        <span className="flex items-center gap-2">
                          <UploadOutlined />
                          Upload
                        </span>
                      ),
                      children: (
                        <div className="space-y-6">
                          {/* Upload Area */}
                          <div className="flex justify-center my-2 w-full">
                            <Upload
                              className="w-full"
                              name="image"
                              listType="picture-card"
                              showUploadList={false}
                              onChange={handleUpload}
                              accept="image/*"
                              customRequest={({ file, onSuccess }) => {
                                setTimeout(() => {
                                  onSuccess?.(file);
                                }, 0);
                              }}
                            >
                              <div className="flex flex-col items-center justify-center py-8 relative z-10">
                                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                                  <UploadOutlined className="text-2xl text-blue-500" />
                                </div>
                              </div>
                            </Upload>
                          </div>

                          {/* Preview Section */}
                          {uploadedImage && (
                            <div className="bg-white border border-gray-200 rounded-lg p-6">
                              <div className="flex items-center gap-2 mb-4">
                                <PictureOutlined className="text-green-500" />
                                <h4 className="font-medium text-gray-700">
                                  Preview
                                </h4>
                              </div>
                              <div className="flex items-center gap-4">
                                <div className="relative">
                                  <img
                                    src={uploadedImage}
                                    alt="Preview"
                                    className="w-12 h-12 rounded-lg border border-gray-200"
                                    style={{ objectFit: "cover" }}
                                  />
                                  <div className="absolute top-2 right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full">
                                    ✓
                                  </div>
                                </div>
                                <div className="flex-1">
                                  <p className="text-sm text-gray-600 mb-2">
                                    Image URL:
                                  </p>
                                  <div className="bg-gray-50 p-3 rounded border text-xs font-mono text-gray-700 break-all">
                                    {uploadedImage}
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}

                          {/* Instructions */}
                          {!uploadedImage && (
                            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                              <div className="flex items-start gap-3">
                                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                  <span className="text-blue-600 text-sm font-medium">
                                    i
                                  </span>
                                </div>
                                <div>
                                  <h4 className="font-medium text-blue-800 mb-1">
                                    How it works
                                  </h4>
                                  <p className="text-sm text-blue-700">
                                    1. Click the upload area to select an image
                                    <br />
                                    2. The image will be uploaded to our server
                                    <br />
                                    3. Click &quot;Fill URL&quot; to add the
                                    image URL to your field
                                  </p>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      ),
                    },
                    {
                      key: "preview",
                      label: (
                        <span className="flex items-center gap-2">
                          <EyeOutlined />
                          Preview
                        </span>
                      ),
                      children: (
                        <div className="flex flex-col items-center">
                          <img
                            src={field.value}
                            alt="Full Preview"
                            className="w-full"
                            style={{ maxHeight: "500px", objectFit: "contain" }}
                          />
                          <div className="mt-4 text-center">
                            <p className="text-sm text-gray-600 break-all">
                              {field.value}
                            </p>
                          </div>
                        </div>
                      ),
                    },
                  ]}
                />
              </Modal>
            </div>
          );
        }}
      />
    );
  },
);

FieldUrl.displayName = "FieldUrl";
