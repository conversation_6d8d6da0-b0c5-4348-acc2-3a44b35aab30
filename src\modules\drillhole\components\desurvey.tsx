/* eslint-disable react-hooks/exhaustive-deps */
import { TablePaginationConfig } from "antd";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";

import { ButtonCommon } from "@/components/common/button-common";
import { TableCommon } from "@/components/common/table-common";

import { downholeRequest } from "../api/downhole.api";

export const Desurvey = ({ id }: { id: string }) => {
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [calculateLoading, setCalculateLoading] = useState(false);
  const [columns, setColumns] = useState<any[]>([]);
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  const fetchData = async (currentPage: number = 1, pageSize: number = 10) => {
    setLoading(true);
    try {
      const skipCount = (currentPage - 1) * pageSize;
      const response: any = await downholeRequest.getDesurveyData({
        DrillHoleId: Number(id),
        skipCount: skipCount,
        maxResultCount: pageSize,
      });

      const _data = response?.result?.items || [];
      setData(_data);

      if (_data.length > 0) {
        const _columnsName = Object.keys(_data[0]);
        const result: any[] = [];
        _columnsName.forEach((d) => {
          switch (d) {
            case "id":
              break;
            case "projectId":
              break;
            case "prospectId":
              break;
            case "drillHoleId":
              break;
            default:
              result.push({
                title: <p className="capitalize">{d}</p>,
                dataIndex: d,
              });
              break;
          }
        });
        setColumns(result);
      }

      // Update pagination with total count from API response
      setPagination((prev) => ({
        ...prev,
        total: response.result?.totalCount || _data.length,
      }));
    } catch {
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [id]);

  const handleTableChange = (paginationConfig: TablePaginationConfig) => {
    const { current = 1, pageSize = 10 } = paginationConfig;
    setPagination(paginationConfig);
    fetchData(current, pageSize);
  };

  const handleCalculateDesurvey = async () => {
    setCalculateLoading(true);
    try {
      const response = await downholeRequest.calculateDesurvey({
        drillHoleId: Number(id),
      });

      if (response.state === "success") {
        toast.success("Desurvey calculation completed successfully");
        // Refresh the data after successful calculation
        fetchData(pagination.current, pagination.pageSize);
      } else {
        toast.error(response.message || "Failed to calculate desurvey");
      }
    } catch {
      toast.error("An error occurred while calculating desurvey");
    } finally {
      setCalculateLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-end">
        <ButtonCommon
          loading={calculateLoading}
          onClick={handleCalculateDesurvey}
          className="btn btn-sm hover:bg-primary-hover bg-primary text-white border-none"
        >
          Calculate Desurvey
        </ButtonCommon>
      </div>
      <TableCommon
        columns={columns}
        dataSource={data}
        loading={loading}
        pagination={pagination}
        onChange={handleTableChange}
      />
    </div>
  );
};
