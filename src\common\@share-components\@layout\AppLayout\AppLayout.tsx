/* eslint-disable @next/next/no-img-element */
/* eslint-disable complexity */
/* eslint-disable jsx-a11y/alt-text */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable max-lines */
/* eslint-disable max-lines-per-function */
"use client";
import {
  BankOutlined,
  ControlOutlined,
  DashboardOutlined,
  KeyOutlined,
  ProjectOutlined,
  SettingOutlined,
} from "@ant-design/icons";
import { AntdRegistry } from "@ant-design/nextjs-registry";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { Button, ConfigProvider, Layout, Menu, Tag } from "antd";
import en_US from "antd/es/locale/en_US";
import { isEmpty, isNumber } from "lodash";
import Image from "next/image";
import Link from "next/link";
import { usePathname, useSearchParams } from "next/navigation";
import NextNProgress from "nextjs-progressbar";
import { useEffect, useState } from "react";
import { AiOutlineCalculator, AiOutlineTranslation } from "react-icons/ai";
import { BsFileBreakFill, BsFillFileEarmarkBreakFill } from "react-icons/bs";
import { CgStyle } from "react-icons/cg";
import { CiBoxList, CiCrop } from "react-icons/ci";
import { FaPlay, FaRegEdit, FaTools } from "react-icons/fa";
import {
  FaArrowLeft,
  FaArrowRight,
  FaBoreHole,
  FaDatabase,
  FaImages,
  FaMobile,
  FaNetworkWired,
  FaPeopleGroup,
  FaRectangleList,
} from "react-icons/fa6";
import { GiAirZigzag, GiClaymoreExplosive, GiStonePile } from "react-icons/gi";
import { GrTest } from "react-icons/gr";
import {
  HiOutlineClipboardList,
  HiOutlineTemplate,
  HiRefresh,
} from "react-icons/hi";
import { ImUpload } from "react-icons/im";
import { IoIosColorPalette } from "react-icons/io";
import {
  IoCubeOutline,
  IoImageSharp,
  IoRadioOutline,
  IoSettingsOutline,
} from "react-icons/io5";
import { LuMerge } from "react-icons/lu";
import {
  MdAcUnit,
  MdInsertPageBreak,
  MdOutlineAccountTree,
  MdOutlineAirlineSeatIndividualSuite,
  MdOutlineFileDownload,
  MdOutlineFileUpload,
} from "react-icons/md";
import { PiMountains, PiPolygonLight } from "react-icons/pi";
import {
  RiAdminLine,
  RiComputerLine,
  RiDeleteBin6Fill,
  RiSurveyFill,
} from "react-icons/ri";
import { SiEventbrite } from "react-icons/si";
import { TbSubtask } from "react-icons/tb";
import { ToastContainer } from "react-toastify";

import { AppImages } from "@/common/configs";
import {
  useAppDispatch,
  useAppSelector,
} from "@/common/vendors/redux/store/hook";
import { UserRoles } from "@/constants/constant";
import { selectDetailAccountSettings } from "@/modules/account-settings/redux/accountSettingsSlice";
import {
  selectUserInfo,
  updateCollapseLogging,
  updateFontSize,
} from "@/modules/auth/redux/userSlice";
import { UserRoleFunction } from "@/modules/user-role-config/model/schema/enum/user-role-config.enum,";
import {
  findPathToKey,
  removeHiddenPages,
  removeObjectHasEmptyChildren,
} from "@/utils/findPathToKey";

import GlobalStyles from "../../@shared/AppGlobalStyles/AppGlobalStyles";
import InfoDrillhole from "./info-drillhole";
import MenuFilter from "./menu-filter";
import { UserHeader } from "./user-header";
export interface IDefaultLayoutProps {
  children: React.ReactNode;
  fillSearchParams?: boolean;
  pageId?: number;
}
const { Content, Sider } = Layout;
const queryClient = new QueryClient();
const collapsedLink = ["/logging"];

export default function AppLayout(props: IDefaultLayoutProps) {
  const { children, fillSearchParams = true } = props;
  const userInfo = useAppSelector(selectUserInfo);
  const accountSettings = useAppSelector(selectDetailAccountSettings);
  const pathNames = usePathname();
  const pathName = "/" + pathNames.split("/")[1];
  const [collapsed, setCollapsed] = useState(collapsedLink.includes(pathName));
  const projectId = useAppSelector((state) => state.user?.userInfo?.projectId);
  const prospectId = useAppSelector(
    (state) => state.user?.userInfo?.prospectId,
  );
  const drillholeIdParams = useSearchParams().get("drillholeId");
  const skipCount = useSearchParams().get("skipCount");
  const itemsMenu = [
    {
      key: "/",
      icon: <DashboardOutlined width={20} height={20} />,
      label: (
        <Link href="/" className="font-roboto">
          Dashboard
        </Link>
      ),
      permissions: [
        UserRoles.Admin,
        UserRoles.Company,
        UserRoles.EditorUser,
        UserRoles.ViewerUser,
      ],
    },
    {
      key: "/images-upload",
      icon: <FaImages />,
      label: <Link href="/images-upload">Images Upload</Link>,
      permissions: [UserRoles.Admin],
    },

    {
      key: "/tenant-management",
      icon: <BankOutlined width={20} height={20} />,
      label: <Link href="/tenant-management?isActive=true">Accounts</Link>,
      permissions: [UserRoles.Admin],
    },

    {
      key: "/drillhole-management",
      icon: <FaBoreHole />,
      label: accountSettings?.collectionName ? (
        <Link href="/drillhole-management?isActive=true">
          {accountSettings?.collectionName}
        </Link>
      ) : (
        <Link href={`/drillhole-management?isActive=true`}>Drill Holes</Link>
      ),
      permissions: [
        UserRoles.Company,
        UserRoles.EditorUser,
        UserRoles.ViewerUser,
      ],
    },

    {
      key: "/images",
      icon: <IoImageSharp />,
      label: <Link href="/images">Viewing</Link>,
      permissions: [
        UserRoles.Company,
        UserRoles.EditorUser,
        UserRoles.ViewerUser,
      ],
    },

    {
      key: "/logging",
      icon: <FaRegEdit />,
      label: (
        <Link
          href={`/logging${
            drillholeIdParams && !isNaN(Number(drillholeIdParams))
              ? `?drillholeId=${drillholeIdParams}&skipCount=${skipCount}`
              : ""
          }`}
        >
          Logging
        </Link>
      ),
      permissions: [
        UserRoles.Company,
        UserRoles.EditorUser,
        UserRoles.ViewerUser,
      ],
    },
    {
      key: "/3d-viewing",
      icon: <IoCubeOutline />,
      label: <Link href="/3d-viewing">3D Viewing</Link>,
      permissions: [
        UserRoles.Company,
        UserRoles.EditorUser,
        UserRoles.ViewerUser,
      ],
    },
    {
      icon: <MdOutlineFileUpload />,
      label: "Load Data",
      key: "/load-data",
      children: [
        {
          key: "/load-images",
          icon: <IoImageSharp />,
          label: <Link href="/load-images">Load Images</Link>,
          permissions: [
            UserRoles.Company,
            UserRoles.EditorUser,
            UserRoles.ViewerUser,
          ],
        },

        {
          key: "/upload-tools",
          icon: <ImUpload />,
          label: <Link href="/upload-tools">Upload Data</Link>,
          permissions: [
            UserRoles.Company,
            UserRoles.EditorUser,
            UserRoles.ViewerUser,
          ],
        },
        {
          key: "/mapping-template",
          icon: <ImUpload />,
          label: <Link href="/mapping-template">Upload Template</Link>,
          permissions: [
            UserRoles.Company,
            UserRoles.EditorUser,
            UserRoles.ViewerUser,
          ],
        },
      ],
    },
    {
      key: "/export-template",
      icon: <MdOutlineFileDownload />,
      label: "Export Data",
      children: [
        {
          key: "/logging-data",
          icon: <HiOutlineTemplate />,
          label: <Link href="/logging-data">Logging</Link>,
          permissions: [
            UserRoles.Company,
            UserRoles.EditorUser,
            UserRoles.ViewerUser,
          ],
        },
        {
          key: "/export-template",
          icon: <HiOutlineTemplate />,
          label: <Link href="/export-template">Images</Link>,
          permissions: [
            UserRoles.Company,
            UserRoles.EditorUser,
            UserRoles.ViewerUser,
          ],
        },
        {
          key: "/events",
          icon: <SiEventbrite />,
          label: <Link href="/events">Events</Link>,
          permissions: [
            UserRoles.Company,
            UserRoles.EditorUser,
            UserRoles.ViewerUser,
          ],
        },
      ],
      permissions: [
        UserRoles.Company,
        UserRoles.EditorUser,
        UserRoles.ViewerUser,
      ],
    },
    {
      icon: <SettingOutlined />,
      label: "Settings",
      key: "/settings",
      children: [
        {
          key: "/mobile-profile",
          icon: <FaMobile />,
          label: <Link href="/mobile-profile">Mobile Profiles</Link>,
          permissions: [
            UserRoles.Admin,
            UserRoles.Company,
            UserRoles.EditorUser,
            UserRoles.ViewerUser,
          ],
        },
        {
          key: "/projects",
          icon: <ProjectOutlined />,
          label: "Projects",
          children: [
            {
              key: "/projects",
              icon: <ProjectOutlined />,
              label: <Link href="/projects?isActive=true">Projects</Link>,
              permissions: [
                UserRoles.Company,
                UserRoles.EditorUser,
                UserRoles.ViewerUser,
              ],
            },
            {
              key: "/prospect-management",
              icon: <TbSubtask />,
              label: (
                <Link href="/prospect-management?isActive=true">Prospects</Link>
              ),
              permissions: [
                UserRoles.Company,
                UserRoles.EditorUser,
                UserRoles.ViewerUser,
              ],
            },
          ],
          permissions: [
            UserRoles.Company,
            UserRoles.EditorUser,
            UserRoles.ViewerUser,
          ],
        },
        {
          icon: <HiRefresh />,
          key: "/process",
          label: "Process",
          children: [
            {
              key: "/prepare-images",
              icon: <CiCrop />,
              label: (
                <Link
                  href={`/prepare-images${
                    drillholeIdParams && !isNaN(Number(drillholeIdParams))
                      ? `?drillholeId=${drillholeIdParams}&skipCount=${skipCount}`
                      : ""
                  }`}
                >
                  Prepare Images
                </Link>
              ),
              permissions: [
                UserRoles.Company,
                UserRoles.EditorUser,
                UserRoles.ViewerUser,
              ],
            },
            {
              key: "/process-images",
              icon: <FaTools />,
              label: (
                <Link
                  href={`/process-images${
                    drillholeIdParams && !isNaN(Number(drillholeIdParams))
                      ? `?drillholeId=${drillholeIdParams}&skipCount=${skipCount}`
                      : ""
                  }`}
                >
                  Advanced
                </Link>
              ),
              permissions: [
                UserRoles.Company,
                UserRoles.EditorUser,
                UserRoles.ViewerUser,
              ],
            },
            {
              key: "/process-batch-images",
              icon: <FaPlay />,
              label: <Link href="/process-batch-images">Batch </Link>,
              permissions: [
                UserRoles.Company,
                UserRoles.EditorUser,
                UserRoles.ViewerUser,
              ],
            },
            {
              key: "/merge-drillhole",
              icon: <LuMerge />,
              label: <Link href="/merge-drillhole">Tools </Link>,
              permissions: [
                UserRoles.Company,
                UserRoles.EditorUser,
                UserRoles.ViewerUser,
              ],
            },
          ],
        },

        {
          icon: <PiMountains />,
          key: "/geology",
          label: "Geology",
          children: [
            {
              key: "/geology-suite",
              icon: <HiOutlineTemplate />,
              label: <Link href={`/geology-suite?isActive=true`}>Suites</Link>,
              permissions: [
                UserRoles.Company,
                UserRoles.EditorUser,
                UserRoles.ViewerUser,
              ],
            },

            {
              key: "/geology-field",
              icon: <CiBoxList />,
              label: (
                <Link
                  href={`/geology-field?projectId=${projectId}&prospectId=${prospectId}&isActive=true`}
                >
                  Fields
                </Link>
              ),
              permissions: [
                UserRoles.Company,
                UserRoles.EditorUser,
                UserRoles.ViewerUser,
              ],
            },
            {
              key: "/rock-groups",
              icon: <MdOutlineAirlineSeatIndividualSuite />,
              label: <Link href="/rock-groups?isActive=true">Rock Groups</Link>,
              permissions: [
                UserRoles.Company,
                UserRoles.EditorUser,
                UserRoles.ViewerUser,
              ],
            },
            {
              key: "/rock-type-management",
              icon: <GiStonePile />,
              label: (
                <Link href="/rock-type-management?isActive=true">
                  Rock Types
                </Link>
              ),
              permissions: [
                UserRoles.Company,
                UserRoles.EditorUser,
                UserRoles.ViewerUser,
              ],
            },
            {
              key: "/rock-types-tree",
              icon: <MdOutlineAccountTree />,
              label: <Link href="/rock-types-tree">Rock Types Tree</Link>,
              permissions: [
                UserRoles.Company,
                UserRoles.EditorUser,
                UserRoles.ViewerUser,
              ],
            },

            {
              key: "/rock-style-management",
              icon: <CgStyle />,
              label: (
                <Link href="/rock-style-management?isActive=true">
                  Drawing Styles
                </Link>
              ),
              permissions: [
                UserRoles.Company,
                UserRoles.EditorUser,
                UserRoles.ViewerUser,
              ],
            },
            {
              key: "/logging-view",
              icon: <CgStyle />,
              label: (
                <Link href="/logging-view?isActive=true">Logging Views</Link>
              ),
              permissions: [
                UserRoles.Company,
                UserRoles.EditorUser,
                UserRoles.ViewerUser,
              ],
            },
          ],
        },
        {
          key: "/geotech",
          icon: <GiAirZigzag />,
          label: "Structure",
          permissions: [
            UserRoles.Company,
            UserRoles.EditorUser,
            UserRoles.ViewerUser,
          ],
          children: [
            {
              key: "/geotech-suite",
              icon: <HiOutlineTemplate />,
              label: (
                <Link href="/geotech-suite?isActive=true">
                  Structure Suites
                </Link>
              ),
            },
            {
              key: "/geotech-structures",
              icon: <BsFileBreakFill />,
              label: (
                <Link href="/geotech-structures?isActive=true">Structures</Link>
              ),
            },
            {
              key: "/geotech-structure-type",
              icon: <BsFillFileEarmarkBreakFill />,
              label: (
                <Link href="/geotech-structure-type?isActive=true">
                  Structure Type
                </Link>
              ),
            },
            {
              key: "/structure-condition",
              icon: <MdInsertPageBreak />,
              label: (
                <Link href="/structure-condition?isActive=true">
                  Structure Condition
                </Link>
              ),
            },
          ],
        },
        {
          key: "/assay",
          icon: <GrTest />,
          label: "Assay",
          permissions: [
            UserRoles.Company,
            UserRoles.EditorUser,
            UserRoles.ViewerUser,
          ],
          children: [
            {
              key: "/assay-suite",
              icon: <HiOutlineTemplate />,
              label: <Link href="/assay-suite?isActive=true">Suites</Link>,
              permissions: [
                UserRoles.Company,
                UserRoles.EditorUser,
                UserRoles.ViewerUser,
              ],
            },

            {
              key: "/assay-attribute",
              icon: <CiBoxList />,
              label: (
                <Link href="/assay-attribute?isActive=true">Attribute</Link>
              ),
              permissions: [
                UserRoles.Company,
                UserRoles.EditorUser,
                UserRoles.ViewerUser,
              ],
            },
            {
              key: "/assay-translation",
              icon: <AiOutlineTranslation />,
              label: (
                <Link href="/assay-translation?isActive=true">Translation</Link>
              ),
              permissions: [
                UserRoles.Company,
                UserRoles.EditorUser,
                UserRoles.ViewerUser,
              ],
            },
          ],
        },
        {
          key: "/geophysics",
          icon: <IoRadioOutline />,
          label: "Geophysics",
          children: [
            {
              key: "/suites",
              icon: <HiOutlineTemplate />,
              label: <Link href="/suites?isActive=true">Suites</Link>,
              permissions: [
                UserRoles.Company,
                UserRoles.EditorUser,
                UserRoles.ViewerUser,
              ],
            },
            {
              key: "/attributes",
              icon: <CiBoxList />,
              label: <Link href="/attributes?isActive=true">Attributes</Link>,
              permissions: [
                UserRoles.Company,
                UserRoles.EditorUser,
                UserRoles.ViewerUser,
              ],
            },
          ],
          permissions: [
            UserRoles.Company,
            UserRoles.EditorUser,
            UserRoles.ViewerUser,
          ],
        },

        // {
        //   key: "/account-management",
        //   icon: <UserOutlined />,
        //   label: <Link href="/account-management?isActive=true">Users</Link>,
        //   permissions: [UserRoles.Admin],
        // },

        {
          key: "/data-config",
          icon: <FaDatabase width={20} height={20} />,
          label: "Data Config",

          permissions: [UserRoles.Company],
          children: [
            {
              key: "/image-type",
              icon: <IoImageSharp />,
              label: <Link href="/image-type?isActive=true">Image Types</Link>,
              permissions: [
                UserRoles.Company,
                UserRoles.EditorUser,
                UserRoles.ViewerUser,
              ],
            },
            {
              key: "/list",
              icon: <FaRectangleList />,
              label: "Validations",
              permissions: [
                UserRoles.Company,
                UserRoles.EditorUser,
                UserRoles.ViewerUser,
              ],
              children: [
                {
                  key: "/number-definitions",
                  icon: <CiBoxList />,
                  label: (
                    <Link href="/number-definitions?isActive=true">
                      Number Definitions
                    </Link>
                  ),
                  permissions: [
                    UserRoles.Company,
                    UserRoles.EditorUser,
                    UserRoles.ViewerUser,
                  ],
                },
                {
                  key: "/pick-lists",
                  icon: <HiOutlineClipboardList />,
                  label: (
                    <Link href="/pick-lists?isActive=true">Pick Lists</Link>
                  ),
                  permissions: [
                    UserRoles.Company,
                    UserRoles.EditorUser,
                    UserRoles.ViewerUser,
                  ],
                },
                {
                  key: "/colours",
                  icon: <IoIosColorPalette />,
                  label: <Link href="/colours?isActive=true">Colours</Link>,
                  permissions: [
                    UserRoles.Company,
                    UserRoles.EditorUser,
                    UserRoles.ViewerUser,
                  ],
                },
                {
                  key: "/units",
                  icon: <MdAcUnit />,
                  label: <Link href="/units?isActive=true">Units</Link>,
                  permissions: [
                    UserRoles.Company,
                    UserRoles.EditorUser,
                    UserRoles.ViewerUser,
                  ],
                },
                {
                  key: "/geology-date",
                  icon: <MdAcUnit />,
                  label: (
                    <Link href="/geology-date?isActive=true">Geology Date</Link>
                  ),
                  permissions: [
                    UserRoles.Company,
                    UserRoles.EditorUser,
                    UserRoles.ViewerUser,
                  ],
                },
                {
                  key: "/geology-description",
                  icon: <MdAcUnit />,
                  label: (
                    <Link href="/geology-description?isActive=true">
                      Geology Description
                    </Link>
                  ),
                  permissions: [
                    UserRoles.Company,
                    UserRoles.EditorUser,
                    UserRoles.ViewerUser,
                  ],
                },
                {
                  key: "/number-range",
                  icon: <MdAcUnit />,
                  label: (
                    <Link href="/number-range?isActive=true">Number Range</Link>
                  ),
                  permissions: [
                    UserRoles.Company,
                    UserRoles.EditorUser,
                    UserRoles.ViewerUser,
                  ],
                },
                {
                  key: "/geology-checkbox",
                  icon: <MdAcUnit />,
                  label: (
                    <Link href="/geology-checkbox?isActive=true">
                      Geology Checkbox
                    </Link>
                  ),
                  permissions: [
                    UserRoles.Company,
                    UserRoles.EditorUser,
                    UserRoles.ViewerUser,
                  ],
                },
                {
                  key: "/geology-latitude",
                  icon: <MdAcUnit />,
                  label: (
                    <Link href="/geology-latitude?isActive=true">
                      Geology Latitude
                    </Link>
                  ),
                  permissions: [
                    UserRoles.Company,
                    UserRoles.EditorUser,
                    UserRoles.ViewerUser,
                  ],
                },
                {
                  key: "/geology-longitude",
                  icon: <MdAcUnit />,
                  label: (
                    <Link href="/geology-longitude?isActive=true">
                      Geology Longitude
                    </Link>
                  ),
                  permissions: [
                    UserRoles.Company,
                    UserRoles.EditorUser,
                    UserRoles.ViewerUser,
                  ],
                },
                {
                  key: "/geology-url",
                  icon: <MdAcUnit />,
                  label: (
                    <Link href="/geology-url?isActive=true">Geology URL</Link>
                  ),
                  permissions: [
                    UserRoles.Company,
                    UserRoles.EditorUser,
                    UserRoles.ViewerUser,
                  ],
                },
              ],
            },

            {
              key: "/dh-calculation",
              icon: <AiOutlineCalculator />,
              label: (
                <Link href="/dh-calculation?isActive=true">
                  RQD Calculations
                </Link>
              ),
              permissions: [
                UserRoles.Company,
                UserRoles.EditorUser,
                UserRoles.ViewerUser,
              ],
            },
            {
              key: "/workflows-management",
              icon: <ControlOutlined />,
              label: (
                <Link href={`/workflows-management?isActive=true`}>
                  Workflows
                </Link>
              ),
              permissions: [
                UserRoles.Company,
                UserRoles.EditorUser,
                UserRoles.ViewerUser,
              ],
            },
            {
              key: "/downhole-survey-type",
              icon: <RiSurveyFill />,
              label: (
                <Link href={`/downhole-survey-type?isActive=true`}>
                  Downhole Survey Type
                </Link>
              ),
              permissions: [
                UserRoles.Company,
                UserRoles.EditorUser,
                UserRoles.ViewerUser,
              ],
            },

            {
              key: "/polygon-management",
              icon: <PiPolygonLight />,
              label: (
                <Link href="/polygon-management?isActive=true">Polygons</Link>
              ),
              permissions: [
                UserRoles.Company,
                UserRoles.EditorUser,
                UserRoles.ViewerUser,
              ],
            },
          ],
        },

        {
          key: "/advanced",
          icon: <GiClaymoreExplosive />,
          label: "Administration",
          permissions: [
            UserRoles.Company,
            UserRoles.EditorUser,
            UserRoles.ViewerUser,
          ],
          children: [
            {
              key: "/account-settings",
              icon: <IoSettingsOutline />,
              label: (
                <Link href="/account-settings?isActive=true">
                  Account Options
                </Link>
              ),
              permissions: [UserRoles.Company],
            },
            {
              key: "/api-key",
              icon: <KeyOutlined />,
              label: <Link href="/api-key?isActive=true">Api Keys</Link>,
              permissions: [UserRoles.Admin, UserRoles.Company],
            },
            {
              key: "/delete-data",
              icon: <RiDeleteBin6Fill />,
              label: <Link href="/delete-data">Delete Data</Link>,
              permissions: [
                UserRoles.Company,
                UserRoles.EditorUser,
                UserRoles.ViewerUser,
              ],
            },
          ],
        },

        {
          key: "/user-admin",
          icon: <RiAdminLine width={20} height={20} />,
          label: "User Admin",
          permissions: [
            UserRoles.Company,
            UserRoles.Admin,
            UserRoles.EditorUser,
            UserRoles.ViewerUser,
          ],
          children: [
            {
              key: "/account-management",
              icon: <FaPeopleGroup width={20} height={20} />,
              label: (
                <Link href="/account-management?isActive=true&maxResultCount=50">
                  Users
                </Link>
              ),
              permissions: [UserRoles.Company],
            },
            {
              key: "/user-role-config",
              icon: <RiComputerLine />,
              label: (
                <Link href="/user-role-config?isActive=true">
                  User Role Config
                </Link>
              ),
              permissions: [UserRoles.Company],
            },
            {
              key: "/work-role",
              icon: <FaNetworkWired />,
              label: <Link href="/work-role?isActive=true">Work Role</Link>,
              permissions: [UserRoles.Company],
            },
          ],
        },
      ],
    },
  ];
  const [openKeys, setOpenKeys] = useState<string[]>([]);
  useEffect(() => {
    const activeSubMenu = pathNames.split("/")[1];
    const path = findPathToKey(itemsMenu, `/${activeSubMenu}`);
    if (path) {
      path.forEach((key) => {
        setOpenKeys((prev) => [...prev, key]);
      });
    }
  }, [pathNames]);
  const filterItemsByPermission = (pages: any, userRoles: string[]) => {
    return pages
      .map((item: any) => {
        if (item.children) {
          const filteredChildren = filterItemsByPermission(
            item.children,
            userRoles,
          );
          if (filteredChildren.length > 0) {
            return {
              ...item,
              children: filteredChildren,
            };
          }
        }
        const findPermission = item?.permissions?.find((permission) =>
          userRoles?.find(
            (role) => role.toLowerCase() === (permission ?? "").toLowerCase(),
          ),
        );
        if (findPermission) {
          return item;
        }
        return null;
      })
      .filter((item: any) => !isEmpty(item));
  };
  const userRoleFunctions = useAppSelector(
    (state) => state.user.userInfo.userRoleFunctions,
  );
  const PAGEINFO = [
    {
      value: UserRoleFunction.ViewModule,
      paths: ["/images"],
    },
    {
      value: UserRoleFunction.Settings,
      paths: [
        "/projects",
        "/prospect-management",

        "/merge-drillhole",

        "/workflows-management",
        "/ai-services",

        "/geology-suite",
        "/geology-field",
        "/rock-groups",
        "/rock-type-management",
        "/rock-types-tree",
        "/rock-style-management",
        "/logging-view",

        "/geotech-suite",
        "/geotech-structures",
        "/geotech-structure-type",
        "/structure-condition",

        "/assay-attribute",
        "/assay-suite",
        "/assay-translation",

        "/suites",
        "/attributes",

        "/number-definitions",
        "/pick-lists",
        "/colours",
        "/units",
        "/geology-date",
        "/geology-description",
        "/number-range",
        "/geology-checkbox",
        "/geology-latitude",
        "/geology-longitude",
        "/geology-url",

        "/image-category",
        "/dh-calculation",
        "/workflows-management",
        "/downhole-survey-type",
        "/ai-services",
        "/polygon-management",

        "/export-template",
        "/events",
        "/number-fields",
        "/rock-select-number",
        "/rock-type-number",

        "/user-role-config",
        "/account-management",
        "/account-settings",

        "/work-role",

        "/3d-viewing",
      ],
    },
    {
      value: UserRoleFunction.Load,
      paths: [
        "/load-images",
        "/upload-file",
        "/upload-geology",
        "/upload-assay",
        "/upload-tools",
        "/mapping-template",
      ],
    },
    {
      value: UserRoleFunction.Logging,
      paths: ["/logging"],
    },
    {
      value: UserRoleFunction.ProcessPrepare,
      paths: ["/prepare-images"],
    },
    {
      value: UserRoleFunction.ProcessAdvanced,
      paths: ["/process-images"],
    },
    {
      value: UserRoleFunction.ProcessBatch,
      paths: ["/process-batch-images"],
    },
  ];
  const HIDDEN_PAGE = PAGEINFO.filter(
    (item) => !userRoleFunctions?.includes(item.value),
  )
    .map((item) => item.paths)
    .flat();

  const isAdmin = useAppSelector(
    (state) => state.user.userInfo.roles,
  )?.includes(UserRoles.Admin);
  const isCompany = useAppSelector(
    (state) => state.user.userInfo.roles,
  )?.includes(UserRoles.Company);
  let filteredItems: any;
  if (isAdmin || isCompany) {
    filteredItems = filterItemsByPermission(itemsMenu, userInfo.roles ?? []);
  } else {
    filteredItems = filterItemsByPermission(
      removeHiddenPages(itemsMenu as any, HIDDEN_PAGE).filter(
        (item: any) => !isEmpty(item),
      ),
      userInfo.roles ?? [],
    );
  }
  const fontSize = useAppSelector((state) => state.user.fontSize);
  const collapseLogging = useAppSelector((state) => state.user.collapseLogging);
  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(updateFontSize(14));
  }, [pathName]);
  const isShowPanel =
    pathName === "/logging" ||
    pathName === "/3d-viewing" ||
    pathName === "/3d-upload";

  const isShowDrillhole = pathName === "/logging";
  const selectedDrillhole = useAppSelector(
    (state) => state.logging.selectedDrillHole,
  );
  const unitSymbol = useAppSelector(
    (state) => state.accountSettings?.detail?.unitsSymbol,
  );
  const drillholeDetail = useAppSelector((state) => state.drillHole?.detail);

  return (
    <GlobalStyles>
      <NextNProgress color="red" showOnShallow={true} />
      <QueryClientProvider client={queryClient}>
        <ConfigProvider
          locale={en_US}
          theme={{
            token: {
              colorPrimary: "#2D669B",
              fontFamily: "Visby",
            },
            components: {
              Select: {
                optionActiveBg: "#d6d2d2",
                fontSize: fontSize,
                fontFamily: "Visby",
              },
              Input: {
                fontSize: fontSize,
                fontFamily: "Visby",
              },
              InputNumber: {
                fontSize: fontSize,
                fontFamily: "Visby",
              },
              DatePicker: {
                fontSize: fontSize,
                fontFamily: "Visby",
              },
              Table: {
                fontSize: fontSize,
                fontFamily: "Visby",
                cellPaddingBlock: 7,
                cellPaddingInline: 7,
              },
              Menu: {
                fontFamily: "Visby",
                fontWeightStrong: 600,
                fontSize: 14,
                colorText: "#000000",
              },

              Button: {
                fontFamily: "Visby",
                colorBgBase: "#2D669B",
                colorPrimary: "#2D669B",
                colorPrimaryBgHover: "#2D669B",
                colorPrimaryText: "#2D669B",
                colorPrimaryTextHover: "#2D669B",
                colorPrimaryTextActive: "#2D669B",
                colorPrimaryHover: "#2D669B",
                colorPrimaryActive: "#2D669B",
                borderColorDisabled: "#2D669B",
                colorTextDisabled: "#2D669B",
              },

              Typography: {
                fontFamily: "Visby",
              },
              Form: {
                fontFamily: "Visby",
              },
              Tabs: {
                fontFamily: "Visby",
              },
            },
          }}
        >
          <AntdRegistry>
            <ToastContainer />
            <Layout style={{ minHeight: "100vh" }}>
              <div className="p-2 bg-white ">
                <div className="flex flex-col md:flex-row justify-between items-center gap-2 ">
                  <div className="flex flex-col md:flex-row items-center w-full">
                    <div className="relative mr-2 w-[230px] h-[46px] overflow-hidden flex items-center justify-center">
                      {accountSettings?.isUseLogo &&
                        accountSettings.logoProduct && (
                          <img
                            src={accountSettings.logoProduct}
                            className="w-[230px] h-[46px] object-cover p-3"
                            style={{ boxSizing: "content-box" }}
                            onError={(e) => {
                              e.currentTarget.src = AppImages.logo;
                            }}
                          ></img>
                        )}
                      {!accountSettings?.isUseLogo && (
                        <Image
                          src={AppImages.logo}
                          width={165}
                          height={46}
                          alt="logo"
                        />
                      )}
                    </div>

                    <div className="flex flex-col md:flex-row gap-2 items-center w-full">
                      <MenuFilter fillSearchParams={fillSearchParams} />
                      <div className="flex justify-between flex-1 w-full">
                        {isNumber(selectedDrillhole?.value) &&
                          isShowDrillhole &&
                          isNumber(drillholeDetail?.maxDepth) && (
                            <div className="flex items-center gap-2">
                              <Tag
                                color="default"
                                className="text-xl font-bold"
                              >
                                {drillholeDetail?.name} Max{" "}
                                {drillholeDetail?.maxDepth}
                                {unitSymbol}
                              </Tag>
                              <InfoDrillhole />
                            </div>
                          )}
                        <div className="h-1 w-1"></div>
                        <UserHeader />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <Layout className="">
                <Sider
                  collapsible
                  width={240}
                  theme="light"
                  collapsed={collapsed}
                  onCollapse={(value) => setCollapsed(value)}
                >
                  <div className="flex items-center justify-center flex-row mt-2 mb-1"></div>
                  {isShowPanel && (
                    <div className="px-6">
                      <Button
                        type="primary"
                        onClick={() => {
                          dispatch(updateCollapseLogging(!collapseLogging));
                        }}
                        icon={
                          collapseLogging ? <FaArrowLeft /> : <FaArrowRight />
                        }
                      />
                    </div>
                  )}

                  <Menu
                    theme="light"
                    selectedKeys={[pathName]}
                    openKeys={openKeys}
                    onOpenChange={setOpenKeys}
                    mode="inline"
                    items={removeObjectHasEmptyChildren(filteredItems)}
                  />
                </Sider>
                <Content style={{ margin: "16px" }}>{children}</Content>
              </Layout>
            </Layout>
          </AntdRegistry>
        </ConfigProvider>
      </QueryClientProvider>
    </GlobalStyles>
  );
}
