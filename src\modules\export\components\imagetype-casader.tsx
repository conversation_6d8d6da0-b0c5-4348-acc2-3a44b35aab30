import { <PERSON><PERSON> } from "antd";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useWatch } from "react-hook-form";

import { SelectCommon } from "@/components/common/select-common";
import imageSubTypeRequest from "@/modules/image-type/api/image-subtype.api";
import { useQueryImageType } from "@/modules/image-type/hooks/useQueryImageType";

export const ImageTypeCascade = ({
  control,
  setValue,
}: {
  control: any;
  setValue: any;
}) => {
  const { data: imageTypes } = useQueryImageType();
  const imageCategoryOptions = useMemo(
    () =>
      imageTypes?.data?.items.map((item) => ({
        label: item.name,
        value: item.id,
      })) || [],
    [imageTypes?.data?.items],
  );

  const [casaderRender, setCasaderRender] = useState<any[]>([]);
  const [imageSubType, setImageSubType] = useState<any[]>([]);

  // Watch form values để sync với state
  const watchedImageTypeIds = useWatch({
    control,
    name: "imageTypeIds",
    defaultValue: [],
  });

  const watchedImageSubTypeIds = useWatch({
    control,
    name: "imageSubTypeIds",
    defaultValue: [],
  });

  // Memoize setValue để tránh re-render
  const memoizedSetValue = useCallback(
    (name: string, value: any) => {
      setValue(name, value);
    },
    [setValue],
  );

  // Load image subtypes khi imageTypeIds thay đổi
  useEffect(() => {
    const loadImageSubTypes = async () => {
      const newCascaderRender: any[] = [];

      for (const imageTypeId of watchedImageTypeIds) {
        try {
          const resImageSubType = await imageSubTypeRequest.getList({
            imageTypeId: imageTypeId,
          });

          const imageTypeOption = imageCategoryOptions.find(
            (opt) => opt.value === imageTypeId,
          );

          const data = {
            label: imageTypeOption?.label || `Type ${imageTypeId}`,
            value: imageTypeId,
            children: resImageSubType?.data?.items.map((item) => ({
              label: item.name,
              value: item.id,
            })),
          };

          newCascaderRender.push(data);
        } catch {
          // Do nothing
        }
      }

      setCasaderRender(newCascaderRender);
    };

    if (watchedImageTypeIds.length > 0) {
      loadImageSubTypes();
    } else {
      setCasaderRender([]);
      setImageSubType([]);
      memoizedSetValue("imageSubTypeIds", []);
    }
  }, [watchedImageTypeIds, imageCategoryOptions, memoizedSetValue]);

  // Sync cascader value từ form khi imageSubTypeIds thay đổi
  useEffect(() => {
    if (watchedImageSubTypeIds.length > 0 && casaderRender.length > 0) {
      const newCascaderValue = watchedImageSubTypeIds
        .map((subTypeId) => {
          const parentImageType = casaderRender.find((imageType) =>
            imageType.children?.some((child: any) => child.value === subTypeId),
          );
          return parentImageType ? [parentImageType.value, subTypeId] : null;
        })
        .filter(Boolean);

      setImageSubType(newCascaderValue);
    } else {
      setImageSubType([]);
    }
  }, [watchedImageSubTypeIds, casaderRender]);

  // Hàm để lấy chỉ giá trị của cấp thứ 2 (children)
  const extractSecondLevelValues = useCallback(
    (selectedOptions: any[], cascaderValue: any[]): number[] => {
      const secondLevelValues: number[] = [];

      // Tạo map để track các parent đã được chọn toàn bộ
      const parentMap = new Map();

      // Xử lý selectedOptions trước
      selectedOptions.forEach((option) => {
        // Kiểm tra nếu option có children (tức là đã chọn đến cấp thứ 2)
        if (option.children && option.children.length > 0) {
          // Lấy tất cả children đã được chọn
          option.children.forEach((child: any) => {
            if (child.value) {
              secondLevelValues.push(child.value);
            }
          });
        }
        // Nếu option không có children nhưng có value, có thể là cấp thứ 2
        else if (option.value && !option.children) {
          secondLevelValues.push(option.value);
        }
      });

      // Xử lý cascaderValue để handle trường hợp chọn tất cả children
      cascaderValue.forEach((item) => {
        if (Array.isArray(item)) {
          if (item.length === 1) {
            // Nếu chỉ có 1 phần tử, có thể là parent được chọn toàn bộ
            const parentValue = item[0];
            parentMap.set(parentValue, true);
          } else if (item.length === 2) {
            // Nếu có 2 phần tử [parent, child], lấy child
            secondLevelValues.push(item[1]);
          }
        }
      });

      // Nếu có parent được chọn toàn bộ, lấy tất cả children của parent đó
      if (parentMap.size > 0) {
        casaderRender.forEach((imageType) => {
          if (parentMap.has(imageType.value) && imageType.children) {
            imageType.children.forEach((child: any) => {
              if (!secondLevelValues.includes(child.value)) {
                secondLevelValues.push(child.value);
              }
            });
          }
        });
      }

      // Loại bỏ duplicates
      return [...new Set(secondLevelValues)];
    },
    [casaderRender],
  );

  return (
    <div className="flex flex-col gap-2">
      <p>Image Type</p>
      <SelectCommon
        control={control}
        name="imageTypeIds"
        options={imageCategoryOptions}
        size="large"
        mode="multiple"
        className="w-full"
        placeholder="Select image type"
      />
      <Cascader.Panel
        multiple
        options={casaderRender}
        value={imageSubType}
        onChange={(value, selectedOptions) => {
          setImageSubType(value);
          // Extract chỉ giá trị của cấp thứ 2
          const secondLevelIds = extractSecondLevelValues(
            selectedOptions,
            value,
          );
          memoizedSetValue("imageSubTypeIds", secondLevelIds);
        }}
      />
    </div>
  );
};
