import { Checkbox } from "antd";
import React, { memo, useCallback } from "react";
import { Control, Controller } from "react-hook-form";

import { ErrorTooltip } from "./error-tooltip";

interface FieldCheckboxProps {
  control: Control<any>;
  name: string;
  disabled?: boolean;
  mandatory?: boolean;
  onKeyDown?: (event: React.KeyboardEvent) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  className?: string;
  id?: string;
  onFieldChange?: (rowIndex: number, fieldPath: string, value: any) => void;
  rowIndex?: number;
  fieldPath?: string;
}

export const FieldCheckbox = memo<FieldCheckboxProps>(
  ({
    control,
    name,
    disabled = false,
    mandatory = false,
    onKeyDown,
    onFocus,
    onBlur,
    className,
    id,
    onFieldChange,
    rowIndex,
    fieldPath,
  }) => {
    const handleKeyDown = useCallback(
      (event: React.KeyboardEvent) => {
        // Handle tab navigation and shortcuts
        onKeyDown?.(event);
      },
      [onKeyDown],
    );

    const handleFocus = useCallback(() => {
      onFocus?.();
    }, [onFocus]);

    const handleBlur = useCallback(() => {
      onBlur?.();
    }, [onBlur]);
    // const currentValues = getValues("dataEntryValues");

    return (
      <Controller
        name={name}
        control={control}
        rules={{
          required: mandatory ? "This field is required" : false,
        }}
        render={({ field, fieldState: { error } }) => {
          return (
            <div className="w-full flex items-center gap-2 justify-center">
              <Checkbox
                id={id}
                disabled={disabled}
                checked={field.value || false}
                className={className || ""}
                onKeyDown={handleKeyDown}
                onFocus={handleFocus}
                onBlur={handleBlur}
                onChange={(e) => {
                  // Cập nhật form value
                  field.onChange(e.target.checked);

                  // Trigger row status update
                  if (
                    onFieldChange &&
                    typeof rowIndex === "number" &&
                    fieldPath
                  ) {
                    onFieldChange(rowIndex, fieldPath, e.target.checked);
                  }
                }}
              />
              <ErrorTooltip error={error} />
            </div>
          );
        }}
      />
    );
  },
);

FieldCheckbox.displayName = "FieldCheckbox";
