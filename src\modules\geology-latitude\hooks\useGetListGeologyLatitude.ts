import { useState } from "react";

import geologyLatitudeRequest from "../api/geology-latitude.api";
import { GeologyLatitudeQuery } from "../interface/geology-latitude.query";

export const useGetListGeologyLatitude = () => {
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any[]>([]);
  const request = async (
    params: GeologyLatitudeQuery,
    onSuccess?: Function,
    onError?: Function,
  ) => {
    setLoading(true);
    const response = await geologyLatitudeRequest.getList({
      ...params,
      isActive: params.isActive ?? true,
    });
    if (response?.state === "success") {
      setData(response.data?.items);
      setTotal(response.data?.pagination?.total);
      setLoading(false);
      onSuccess?.(response.data);
      return response.data;
    } else {
      onError?.(response);
      setLoading(false);
      return null;
    }
  };

  return { request, loading, data, total };
};
