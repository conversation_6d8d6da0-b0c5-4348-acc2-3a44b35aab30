/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable max-lines */
/* eslint-disable max-lines-per-function */
import { ModalType } from "@/common/configs/app.enum";
import { SortOrder } from "@/common/interfaces/general/general.types";
import {
  useAppDispatch,
  useAppSelector,
} from "@/common/vendors/redux/store/hook";
import { ButtonCommon } from "@/components/common/button-common";
import ColorPickerCommon from "@/components/common/color-picker";
import { InputNumberCommon } from "@/components/common/input-number";
import { InputTextCommon } from "@/components/common/input-text";
import { ModalCommon } from "@/components/common/modal-common";
import { SelectCommon } from "@/components/common/select-common";
import { TextAreaCommon } from "@/components/common/textarea-common";
import { ToogleCommon } from "@/components/common/toogle-common";
import { useGetListAssaySuite } from "@/modules/assay/hooks/useGetListAssaySuite";
import {
  selectUserInfo,
  toggleProjectAndProspect,
} from "@/modules/auth/redux/userSlice";
import { useGetListDhCalculation } from "@/modules/dh-calculation/hooks/useGetListDhCalculation";
import { useGetListSuite } from "@/modules/downhole-point/hooks/useGetListSuite.hook";
import { useGetListGeologySuite } from "@/modules/geology-suite/hooks/useGetListGeologySuite";
import { useGetListGeotechSuite } from "@/modules/geotech-suite/hooks/useGetListGeotechSuite";
import { useQueryImageType } from "@/modules/image-type/hooks/useQueryImageType";
import { useGetListLoggingView } from "@/modules/logging-view/hooks/useGetListLoggingView";
import { useQueryMobileProfile } from "@/modules/mobile-profile/hooks/useQueryMobileProfile";
import { useGetListPolygon } from "@/modules/polygon/hooks/useGetListPolygon.hook";
import { useGetListRockGroup } from "@/modules/rock-groups/hooks/useGetListRockGroups";
import { useQueryRockGroup } from "@/modules/rock-groups/hooks/useQueryRockGroup";
import { useGetListWorkflows } from "@/modules/workflows/hooks/useGetListWorkflow.hook";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form, Tabs } from "antd";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-toastify";
import { useCreateProject, useDeleteProject, useUpdateProject } from "../hooks";
import { useAssignAssaySuite } from "../hooks/useAssignAssaySuite";
import { useAssignProject } from "../hooks/useAssignProject.hook";
import { useGetDetailProject } from "../hooks/useGetDetailProject";
import { ProjectBody, ProjectBodyType } from "../model/schema/project.schema";

export interface IModalCompanyProps {
  modalState: {
    isOpen: boolean;
    detailInfo: any;
    type: string;
  };
  setModalState: (value: any) => void;
  fetchListProject: () => void;
}
/* eslint-disable max-len */
export function ModalProject(props: IModalCompanyProps) {
  const user = useAppSelector(selectUserInfo);
  const dispatch = useAppDispatch();
  const tenantId = user.tenantId;
  const { modalState, setModalState, fetchListProject } = props;
  const { request: requestCreateProject, loading: loadingCreateProject } =
    useCreateProject();
  const { request: requestUpdateProject, loading: loadingUpdateProject } =
    useUpdateProject();
  const { request: requestDeleteProject, loading: loadingDeleteProject } =
    useDeleteProject();
  const { data: workflows, request: requestGetListWorkflows } =
    useGetListWorkflows();
  const [isEndWorkflow, setEndWorkflow] = useState(false);
  const [maxResultCountWorkflow, setMaxResultCountWorkflow] = useState(100);
  const [activeTab, setActiveTab] = useState("1");

  const [requestGetListPolygons] = useGetListPolygon();
  const [loading, setLoading] = useState(false);
  const [listPolygons, setListPolygons] = useState<any[]>([]);
  const [boundingBoxes, setBoundingBoxes] = useState<any[]>([]);
  const [boundingRows, setBoundingRows] = useState<any[]>([]);

  useEffect(() => {
    if (listPolygons) {
      setBoundingBoxes(
        listPolygons
          ?.filter((polygon: any) => polygon?.type === 1)
          .map((item: any) => {
            return {
              value: item?.id,
              label: item?.name,
            };
          }),
      );
      setBoundingRows(
        listPolygons
          ?.filter((polygon: any) => polygon?.type === 2)
          .map((item: any) => {
            return {
              value: item?.id,
              label: item?.name,
            };
          }),
      );
    }
  }, [listPolygons]);

  useEffect(() => {
    requestGetListPolygons(
      {},
      setLoading,
      (res: any) => {
        setListPolygons(res.items);
      },
      () => {},
    );
  }, []);

  const handleCancel = () => {
    setModalState({ ...modalState, isOpen: false });
  };

  const { control, handleSubmit, getValues, setValue, reset } =
    useForm<ProjectBodyType>({
      resolver: zodResolver(ProjectBody),
      defaultValues: {
        backgroundColor: modalState?.detailInfo?.backgroundColor || "#000000",
        textColor: modalState?.detailInfo?.textColor || "#FFFFFF",
        isActive: modalState?.detailInfo?.isActive ?? true,
      },
    });

  const isConfirm = modalState.type === ModalType.DELETE;

  const onSubmit = (values: ProjectBodyType) => {
    if (modalState.type === ModalType.CREATE) {
      requestCreateProject(
        {
          ...values,
          tenantId: tenantId as any,
        },
        () => {
          setModalState({ ...modalState, isOpen: false });
          toast.success("Create project successfully");
          fetchListProject();
          dispatch(toggleProjectAndProspect());
        },
        (error) => {
          toast.error(error);
        },
      );
    }
    if (modalState.type === ModalType.UPDATE) {
      requestUpdateProject(
        {
          ...values,
          tenantId: tenantId as any,
          id: modalState.detailInfo.id,
        },
        () => {
          setModalState({ ...modalState, isOpen: false });
          toast.success("Update project successfully");
          fetchListProject();
          dispatch(toggleProjectAndProspect());
        },
        (error) => {
          toast.error(error);
        },
      ).then(() => {});
    }
  };

  const handleDelete = () => {
    requestDeleteProject(
      {
        id: modalState.detailInfo.id,
      },
      () => {
        setModalState({ ...modalState, isOpen: false });
        fetchListProject();
        dispatch(toggleProjectAndProspect());
      },
      () => {},
    ).then(() => {});
  };

  const handleScrollWorkflow = (event: any) => {
    const target = event.target;
    if (
      target.scrollTop + target.offsetHeight === target.scrollHeight &&
      !isEndWorkflow
    ) {
      setMaxResultCountWorkflow(maxResultCountWorkflow + 10);
    }
  };

  const { request: requestRockGroup } = useGetListRockGroup();
  useEffect(() => {
    requestRockGroup({
      page: 1,
      pageSize: 100,
      sortField: "name",
      sortOrder: SortOrder.ASC,
    });
  }, []);

  useEffect(() => {
    if (workflows?.length <= maxResultCountWorkflow) {
      setEndWorkflow(true);
    } else {
      setEndWorkflow(false);
    }
  }, [maxResultCountWorkflow, workflows]);

  useEffect(() => {
    requestGetListWorkflows({
      maxResultCount: maxResultCountWorkflow,
      skipCount: 0,
      isActive: true,
      sortField: "name",
      sortOrder: "asc",
    });
  }, [maxResultCountWorkflow]);
  const { loading: loadingAssignProject } = useAssignProject();

  const { loading: loadingAssignAssaySuite } = useAssignAssaySuite();

  useEffect(() => {
    requestGetDetailProject(modalState?.detailInfo?.id, (res) => {
      const imageTypeIds = res?.imageTypes?.map((item: any) => item.id);
      reset({
        name: res.name,
        code: res.code,
        description: res.description,
        coreTrayLength: res.coreTrayLength,
        boundingBoxId: res.boundingBoxIdArr,
        boundingRowsId: res.boundingRowsIdAr,
        isActive: res.isActive,
        mobileWorkflowId: res.workflow?.id,
        loggingViewIds: res.loggingViews?.map((item: any) => item.id),
        suiteIds: res.geophysicsSuites?.map((item: any) => item.id),
        assaySuiteIds: res.assaySuites?.map((item: any) => item.id),
        geologySuiteIds: res.geologySuites?.map((item: any) => item.id),
        geotechSuiteIds: res?.geotechSuites?.map((item: any) => item.id),
        rqdCalculationIds: res.rqdCalculations?.map((item: any) => item.id),
        backgroundColor: res.backgroundColor,
        textColor: res.textColor,
        imageTypeIds: imageTypeIds,
        rockGroupId: res.rockGroup?.id,
        mobileProfileId: res.mobileProfile?.id,
      });
    });
  }, [modalState?.detailInfo?.id]);
  const { request: requestListSuite, data: listSuite } = useGetListSuite();
  const { data: geologySuite, request: requestGeologySuite } =
    useGetListGeologySuite();

  const [keywordGeologySuite] = useState("");
  useEffect(() => {
    if (!modalState.isOpen) {
      return;
    }
    if (modalState?.detailInfo?.id) {
      requestListSuite({
        keyword: keywordGeologySuite,
        maxResultCount: 100,
        skipCount: 0,
      });
      requestGeologySuite({
        keyword: keywordGeologySuite,
        maxResultCount: 100,
        skipCount: 0,
        isActive: true,
      });
    }
  }, [modalState.detailInfo?.id, modalState.isOpen]);
  const { request: requestGetDetailProject } = useGetDetailProject();

  const { data: listAssaySuite, request: requestListAssaySuite } =
    useGetListAssaySuite();
  const [keywordAssaySuite] = useState("");
  useEffect(() => {
    if (modalState?.detailInfo?.id) {
      requestListAssaySuite({
        keyword: keywordAssaySuite,
        maxResultCount: 100,
        skipCount: 0,
      });
    }
  }, [modalState.isOpen, keywordAssaySuite, modalState?.detailInfo?.id]);
  const { data: loggingViews, request: requestLoggingViews } =
    useGetListLoggingView();
  const [keywordLoggingView, setKeywordLoggingView] = useState("");
  useEffect(() => {
    requestLoggingViews({
      keyword: keywordLoggingView,
      maxResultCount: 100,
      skipCount: 0,
    });
  }, [modalState.isOpen, keywordLoggingView]);
  const { data: geotechSuite, request: requestGeotechSuite } =
    useGetListGeotechSuite();
  const [keywordGeotechSuite, setKeywordGeotechSuite] = useState("");
  useEffect(() => {
    if (modalState?.detailInfo?.id) {
      requestGeotechSuite({
        keyword: keywordGeotechSuite,
        maxResultCount: 100,
        skipCount: 0,
      });
    }
  }, [modalState.isOpen, keywordGeotechSuite, modalState?.detailInfo?.id]);
  const dataTab = [
    {
      key: "1",
      label: "Details",
      items: [
        "name",
        "code",
        "description",
        "backgroundColor",
        "textColor",
        "isActive",
      ],
    },
    {
      key: "2",
      label: "Settings",
      items: [
        "coreTrayLength",
        "boundingBoxId",
        "boundingRowsId",
        "mobileWorkflowId",
        "imageTypeId",
        "loggingViewIds",
        "imageTypeIds",
      ],
    },
    {
      key: "3",
      label: "Assign Suite",
      items: [
        "suiteIds",
        "assaySuiteIds",
        "geologySuiteIds",
        "geotechSuiteIds",
      ],
    },
  ];
  const [keywordDhCalculation, setKeywordDhCalculation] = useState("");
  const { data: listDhCalculation, request: requestListDhCalculation } =
    useGetListDhCalculation();
  useEffect(() => {
    requestListDhCalculation({
      maxResultCount: 100,
      keyword: keywordDhCalculation,
    });
  }, [keywordDhCalculation]);

  const {
    data: imageTypes,
    setSearchParams: setImageTypeSearchParams,
    searchParams: imageTypeSearchParams,
    setEnable: setEnableImageType,
    handleScroll: handleScrollImageType,
  } = useQueryImageType();
  useEffect(() => {
    setEnableImageType(true);
  }, []);
  const {
    data: rockGroups,
    setEnable: setEnableRockGroup,
    handleScroll: handleScrollRockGroup,
    searchParams: searchParamsRockGroup,
    setSearchParams: setSearchParamsRockGroup,
  } = useQueryRockGroup();
  useEffect(() => {
    setEnableRockGroup(true);
  }, []);
  const {
    data: mobileProfiles,
    setEnable: setEnableMobileProfile,
    handleScroll: handleScrollMobileProfile,
    searchParams: searchParamsMobileProfile,
    setSearchParams: setSearchParamsMobileProfile,
  } = useQueryMobileProfile();
  useEffect(() => {
    setEnableMobileProfile(true);
  }, []);
  const accountSettings = useAppSelector((state) => state.accountSettings);
  return (
    <ModalCommon
      open={modalState.isOpen}
      centered
      padding={0}
      footer={null}
      onCancel={handleCancel}
      style={{ borderRadius: 8 }}
      width={450}
      closable={false}
    >
      {isConfirm ? (
        <div className="flex flex-col gap-2">
          <p className="font-bold text-16-18 capitalize font-visby">
            Are you sure you want to delete this project?
          </p>
          <p>
            This action cannot be undone. This will permanently delete the
            project
          </p>
          <div className="flex justify-end gap-2">
            <ButtonCommon onClick={handleCancel} className="btn btn-sm">
              No
            </ButtonCommon>
            <ButtonCommon
              loading={loadingDeleteProject}
              onClick={handleDelete}
              className="btn btn-sm bg-primary text-white hover:bg-primary"
            >
              Yes
            </ButtonCommon>
          </div>
        </div>
      ) : (
        <div className="px-6 flex flex-col gap-4">
          <p className="font-bold text-24-28 capitalize text-center font-visby">
            {modalState.type === ModalType.UPDATE
              ? "Update project"
              : "Add project"}
          </p>
          <Form
            onFinish={handleSubmit(onSubmit, (error) => {
              const keys = Object.keys(error);
              const tab = dataTab.find((item) =>
                item.items.some((item) => keys.includes(item)),
              );
              if (tab) {
                setActiveTab(tab.key);
              }
            })}
            className="flex flex-col gap-3"
          >
            <Tabs
              defaultActiveKey="1"
              activeKey={activeTab}
              onChange={(key) => setActiveTab(key)}
              items={[
                {
                  key: "1",
                  label: "Details",
                  children: (
                    <div className="flex flex-col gap-3 mt-2">
                      <InputTextCommon
                        label="Name"
                        name="name"
                        placeholder="Type project name here"
                        control={control}
                        isRequired={true}
                      />
                      <InputTextCommon
                        label="Code"
                        name="code"
                        placeholder="Type project code here"
                        control={control}
                        isRequired={true}
                      />
                      <TextAreaCommon
                        label="Description"
                        name="description"
                        placeholder="Type project description here"
                        control={control}
                        isRequired
                      />
                      <ColorPickerCommon
                        getValues={getValues}
                        setValue={setValue}
                        control={control}
                        name="backgroundColor"
                        label="Background Color"
                        isRequired={true}
                        placeholder="Choose background color here"
                      />
                      <ColorPickerCommon
                        setValue={setValue}
                        getValues={getValues}
                        control={control}
                        name="textColor"
                        label="Text Color"
                        placeholder="Choose text color here"
                        isRequired={true}
                      />

                      <ToogleCommon
                        label="Active"
                        control={control}
                        name="isActive"
                      />
                    </div>
                  ),
                },
                {
                  key: "3",
                  label: "Assign Suites",
                  children: (
                    <div className="flex flex-col gap-3 mt-2">
                      <SelectCommon
                        label="Geophysics Suite"
                        control={control}
                        name="suiteIds"
                        options={listSuite?.map((item: any) => {
                          return {
                            value: item.id,
                            label: item.name,
                          };
                        })}
                        optionFilterProp="label"
                        placeholder="Choose suites"
                        mode="multiple"
                        allowClear
                      />
                      <SelectCommon
                        label="Assay Suite"
                        control={control}
                        name="assaySuiteIds"
                        options={listAssaySuite?.map((item: any) => {
                          return {
                            value: item.id,
                            label: item.name,
                          };
                        })}
                        optionFilterProp="label"
                        placeholder="Choose suites"
                        mode="multiple"
                        allowClear
                      />
                      <SelectCommon
                        label="Geology Suite"
                        control={control}
                        name="geologySuiteIds"
                        options={geologySuite?.map((item: any) => {
                          return {
                            value: item.id,
                            label: item.name,
                          };
                        })}
                        optionFilterProp="label"
                        placeholder="Choose suites"
                        mode="multiple"
                        allowClear
                      />
                      <SelectCommon
                        label="Geotech Suite"
                        control={control}
                        name="geotechSuiteIds"
                        options={geotechSuite?.map((item: any) => {
                          return {
                            value: item.id,
                            label: item.name,
                          };
                        })}
                        optionFilterProp="label"
                        placeholder="Choose geotech suites"
                        mode="multiple"
                        allowClear
                        onSearch={(value) => {
                          setKeywordGeotechSuite(value);
                        }}
                        showSearch
                        searchValue={keywordGeotechSuite}
                      />
                    </div>
                  ),
                },
                {
                  key: "2",
                  label: "Settings",
                  children: (
                    <div className="flex flex-col gap-3 mt-2">
                      <InputNumberCommon
                        label={`Core tray length (${accountSettings.detail?.unitsSymbol})`}
                        name="coreTrayLength"
                        placeholder="Type core tray length here"
                        control={control}
                        isRequired={true}
                      />
                      <SelectCommon
                        label="Bounding Box"
                        control={control}
                        name="boundingBoxId"
                        options={boundingBoxes}
                        optionFilterProp="label"
                        placeholder="Select a bounding box"
                        mode="multiple"
                        allowClear
                        isRequired={true}
                      />

                      <SelectCommon
                        label="Bounding Row"
                        control={control}
                        name="boundingRowsId"
                        options={boundingRows}
                        optionFilterProp="label"
                        placeholder="Select a bounding row"
                        mode="multiple"
                        allowClear
                        isRequired={true}
                      />

                      <SelectCommon
                        label="Mobile Image Workflow"
                        control={control}
                        name="mobileWorkflowId"
                        options={workflows.map((workflow) => {
                          return {
                            label: workflow.name,
                            value: workflow.id,
                          };
                        })}
                        optionFilterProp="label"
                        placeholder="Select a workflow for mobile image upload"
                        onPopupScroll={handleScrollWorkflow}
                        filterOption={false}
                        onSearch={(value) => {
                          requestGetListWorkflows({
                            maxResultCount: 100,
                            skipCount: 0,
                            keyword: value,
                            isActive: true,
                          });
                        }}
                        allowClear
                      />
                    </div>
                  ),
                },

                {
                  key: "4",
                  label: "Advanced",
                  children: (
                    <div className="flex flex-col gap-3 mt-2">
                      <SelectCommon
                        label="Image Types"
                        name="imageTypeIds"
                        control={control}
                        mode="multiple"
                        placeholder="Select an image type"
                        options={imageTypes?.data?.items?.map((item: any) => ({
                          value: item.id,
                          label: item.name,
                        }))}
                        optionFilterProp="label"
                        searchValue={imageTypeSearchParams?.keyword}
                        filterOption={false}
                        onSearch={(value) => {
                          setImageTypeSearchParams((prev: any) => ({
                            ...prev,
                            keyword: value,
                          }));
                        }}
                        showSearch
                        allowClear
                        onPopupScroll={handleScrollImageType}
                        onSelect={() => {
                          setImageTypeSearchParams((prev: any) => ({
                            ...prev,
                            keyword: "",
                          }));
                        }}
                      />
                      <SelectCommon
                        label="Mobile Profile"
                        control={control}
                        name="mobileProfileId"
                        options={mobileProfiles?.data?.items?.map(
                          (item: any) => {
                            return {
                              value: item.id,
                              label: item.name,
                            };
                          },
                        )}
                        optionFilterProp="label"
                        placeholder="Select a mobile profile"
                        allowClear
                        onSearch={(value) => {
                          setSearchParamsMobileProfile((prev: any) => ({
                            ...prev,
                            keyword: value,
                          }));
                        }}
                        showSearch
                        searchValue={searchParamsMobileProfile?.keyword}
                        onPopupScroll={handleScrollMobileProfile}
                        filterOption={false}
                      />
                      <SelectCommon
                        label="Logging Views"
                        control={control}
                        name="loggingViewIds"
                        options={loggingViews?.map((item: any) => {
                          return {
                            value: item.id,
                            label: item.name,
                          };
                        })}
                        optionFilterProp="label"
                        placeholder="Select a logging view"
                        mode="multiple"
                        allowClear
                        onSearch={(value) => {
                          setKeywordLoggingView(value);
                        }}
                        showSearch
                        searchValue={keywordLoggingView}
                      />
                      <SelectCommon
                        label="RQD Calculations"
                        control={control}
                        name="rqdCalculationIds"
                        options={listDhCalculation?.map((item: any) => {
                          return {
                            value: item.id,
                            label: item.name,
                          };
                        })}
                        optionFilterProp="label"
                        placeholder="Select a rqd calculation"
                        mode="multiple"
                        allowClear
                        onSearch={(value) => {
                          setKeywordDhCalculation(value);
                        }}
                        showSearch
                        searchValue={keywordDhCalculation}
                      />
                      <SelectCommon
                        label="Colour Map"
                        control={control}
                        name="rockGroupId"
                        options={rockGroups?.data?.items?.map((item: any) => {
                          return {
                            value: item.id,
                            label: item.name,
                          };
                        })}
                        placeholder="Select a rock group"
                        filterOption={false}
                        onSearch={(value) => {
                          setSearchParamsRockGroup((prev: any) => ({
                            ...prev,
                            keyword: value,
                          }));
                        }}
                        showSearch
                        searchValue={searchParamsRockGroup?.keyword}
                        allowClear
                        onPopupScroll={handleScrollRockGroup}
                      />
                    </div>
                  ),
                },
              ]}
              className="mb-3"
            />

            <div className="flex flex-col gap-3 mt-3">
              <ButtonCommon
                loading={
                  loading ||
                  loadingCreateProject ||
                  loadingUpdateProject ||
                  (activeTab === "3" &&
                    (loadingAssignProject || loadingAssignAssaySuite))
                }
                type="submit"
                className="btn btn-sm w-full hover:bg-primary-hover bg-primary text-white border-none"
              >
                {activeTab === "3"
                  ? "Assign Suite"
                  : modalState.type === ModalType.UPDATE
                    ? "Update project"
                    : "Add project"}
              </ButtonCommon>
              <ButtonCommon
                onClick={handleCancel}
                className="btn py-2 w-full btn-sm bg-slate-400 text-white border-none hover:bg-slate-500"
              >
                Cancel
              </ButtonCommon>
            </div>
          </Form>
        </div>
      )}
    </ModalCommon>
  );
}
