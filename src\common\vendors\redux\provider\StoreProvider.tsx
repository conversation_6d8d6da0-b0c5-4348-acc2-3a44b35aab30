"use client";

import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { Provider } from "react-redux";
import { PersistGate } from "redux-persist/integration/react";

import { persistor, reduxStore } from "@/common/vendors/redux/store/store";

const queryClient = new QueryClient();
export const StoreProvider = (props: React.PropsWithChildren) => {
  return (
    <QueryClientProvider client={queryClient}>
      <Provider store={reduxStore}>
        <PersistGate loading={null} persistor={persistor}>
          {props.children}
        </PersistGate>
      </Provider>
    </QueryClientProvider>
  );
};
