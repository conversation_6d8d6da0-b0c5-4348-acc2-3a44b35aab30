import { useAppSelector } from "@/common/vendors/redux/store/hook";
import { calculateColorSimilarity } from "@/utils/color.utils";

export function PixelColorPicker({
  tooltipPosition,
  colorInfo,
}: {
  tooltipPosition: { x: number; y: number };
  colorInfo: { hex: string; rgb: string };
}) {
  const { rockGroupDetail } = useAppSelector((state) => state.logging);

  // Filter rockTypes with similarity > 70%
  const similarRockTypes =
    rockGroupDetail?.rockTypes?.filter((rockType) => {
      const similarity = calculateColorSimilarity(
        colorInfo.hex,
        rockType.rockStyle?.fillColor || "#000000",
      );
      return similarity > 85;
    }) || [];

  return (
    <div
      className="fixed z-50 bg-black text-white px-3 py-2 rounded text-sm font-mono shadow-lg border border-gray-600"
      style={{
        left: tooltipPosition.x,
        top: tooltipPosition.y,
        pointerEvents: "none",
      }}
    >
      <div className="flex items-center gap-2">
        <div
          className="w-4 h-4 border border-white"
          style={{ backgroundColor: colorInfo.hex }}
        />
        <div>
          <div className="font-bold">{colorInfo.hex}</div>
          <div className="text-xs text-gray-300">
            {similarRockTypes.length > 0 ? (
              similarRockTypes.map((rockType) => {
                const similarity = calculateColorSimilarity(
                  colorInfo.hex,
                  rockType.rockStyle?.fillColor || "#000000",
                );

                return (
                  <div
                    key={rockType.id}
                    className="grid grid-cols-4 gap-2 items-center"
                  >
                    <div
                      className="w-full h-full rounded-full p-2"
                      style={{ backgroundColor: rockType.rockStyle?.fillColor }}
                    ></div>
                    <p className="font-bold text-sm col-span-2">
                      {rockType.code}
                    </p>
                    <p className="text-xs text-blue-300">{similarity}%</p>
                  </div>
                );
              })
            ) : (
              <div className="text-red-400 font-bold">NOT FOUND ROCK TYPE</div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
