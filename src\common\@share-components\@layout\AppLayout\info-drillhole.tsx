import { InfoCircleOutlined } from "@ant-design/icons";
import { Button } from "antd";
import { useEffect, useState } from "react";

import { useAppSelector } from "@/common/vendors/redux/store/hook";
import { ModalCommon } from "@/components/common/modal-common";
import { TableCommon } from "@/components/common/table-common";
import drillholeRequest from "@/modules/drillhole/api/drillhole.api";

export default function InfoDrillhole() {
  const [open, setOpen] = useState(false);
  const [data, setdata] = useState<any>(null);
  const drillholeDetail = useAppSelector((state) => state.drillHole?.detail);

  useEffect(() => {
    const getTotalImageTypeByDrillHole = async () => {
      const response = await drillholeRequest.getTotalImageTypeByDrillHole({
        drillHoleId: drillholeDetail?.id,
      });
      setdata(response.data);
    };
    getTotalImageTypeByDrillHole();
  }, [drillholeDetail]);
  const columns = [
    {
      title: "Image Type",
      dataIndex: "imageType",
      render: (value) => {
        return <>{value?.name}</>;
      },
    },
    {
      title: "Total",
      dataIndex: "total",
    },
  ];

  return (
    <div>
      <Button icon={<InfoCircleOutlined />} onClick={() => setOpen(true)} />
      <ModalCommon
        open={open}
        onCancel={() => setOpen(false)}
        footer={false}
        title="Drillhole Info"
        centered
      >
        <div>
          <TableCommon pagination={false} columns={columns} dataSource={data} />
        </div>
      </ModalCommon>
    </div>
  );
}
