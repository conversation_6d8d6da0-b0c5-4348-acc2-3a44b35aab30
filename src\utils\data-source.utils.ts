export type DataType = "geology" | "assay" | "geophysics" | "geotech";

export interface DataSourceParams {
  type: DataType;
  newLoggings: any[];
  assayData: any[];
  geotechData: any[];
  downholeData: any[];
}

export const getDataSource = ({
  type,
  newLoggings,
  assayData,
  geotechData,
  downholeData,
}: DataSourceParams) => {
  switch (type) {
    case "geology":
      return newLoggings;
    case "assay":
      return assayData;
    case "geotech":
      return geotechData;
    case "geophysics":
      return downholeData;
    default:
      return [];
  }
};

export const getLoadingState = ({
  type,
  loadingGeologyData,
  loadingAssayData,
  loadingGeotechData,
  loadingDownholeData,
}: {
  type: DataType;
  loadingGeologyData: boolean;
  loadingAssayData: boolean;
  loadingGeotechData: boolean;
  loadingDownholeData: boolean;
}) => {
  switch (type) {
    case "geology":
      return loadingGeologyData;
    case "assay":
      return loadingAssayData;
    case "geotech":
      return loadingGeotechData;
    case "geophysics":
      return loadingDownholeData;
    default:
      return false;
  }
};
