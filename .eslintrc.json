{
  "root": true,
  "env": {
    "browser": true,
    "es2021": true,
    "node": true
  },
  "extends": ["next/core-web-vitals", "prettier"],
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "ecmaFeatures": {
      "jsx": true
    },
    "ecmaVersion": "latest",
    "sourceType": "module"
  },
  "plugins": ["prettier", "unused-imports", "simple-import-sort"],
  "rules": {
    // Prettier integration
    "prettier/prettier": [
      "error",
      {
        "endOfLine": "auto"
      }
    ],

    // Import sorting and organization
    "simple-import-sort/imports": "off",
    "simple-import-sort/exports": "off",
    "unused-imports/no-unused-imports": "error",
    "unused-imports/no-unused-vars": [
      "error",
      {
        "vars": "all",
        "varsIgnorePattern": "^_",
        "args": "after-used",
        "argsIgnorePattern": "^_"
      }
    ],

    // TypeScript specific rules (handled by Next.js config)
    // These are managed by next/core-web-vitals

    // React specific rules
    "react/react-in-jsx-scope": "off", // Not needed in Next.js
    "react/prop-types": "off", // Using TypeScript for prop validation
    "react/display-name": "off",
    "react-hooks/rules-of-hooks": "error",
    "react-hooks/exhaustive-deps": "warn",

    // General JavaScript/ES6+ rules
    "no-console": "warn",
    "no-debugger": "error",
    "no-alert": "error",
    "no-var": "error",
    "prefer-const": "error",
    "no-unused-expressions": "error",
    "no-duplicate-imports": "error",
    "no-multiple-empty-lines": [
      "error",
      {
        "max": 1,
        "maxEOF": 0
      }
    ],
    "eqeqeq": ["error", "always"],
    "curly": ["error", "all"],

    // Code quality rules
    "complexity": ["warn", 15],
    "max-depth": ["warn", 4],
    "max-lines": ["warn", 500],
    "max-lines-per-function": ["warn", 500],
    "max-params": ["warn", 4],

    // Accessibility rules (already included via next/core-web-vitals)
    "jsx-a11y/alt-text": "error",
    "jsx-a11y/anchor-has-content": "error",
    "jsx-a11y/anchor-is-valid": "error",

    // Performance rules
    "react/jsx-no-bind": [
      "warn",
      {
        "allowArrowFunctions": true,
        "allowBind": false,
        "ignoreRefs": true
      }
    ]
  },
  "overrides": [
    {
      "files": ["*.test.ts", "*.test.tsx", "*.spec.ts", "*.spec.tsx"],
      "env": {
        "jest": true
      },
      "rules": {
        "max-lines-per-function": "off"
      }
    },
    {
      "files": ["next.config.js", "tailwind.config.ts", "postcss.config.js"],
      "rules": {
        "no-console": "off"
      }
    }
  ],
  "settings": {
    "react": {
      "version": "detect"
    },
    "import/resolver": {
      "typescript": {
        "alwaysTryTypes": true,
        "project": "./tsconfig.json"
      }
    }
  },
  "ignorePatterns": [
    "node_modules/",
    ".next/",
    "out/",
    "build/",
    "dist/",
    "*.min.js",
    "public/"
  ]
}
