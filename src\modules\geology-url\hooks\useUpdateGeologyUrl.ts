import { useState } from "react";

import geologyUrlRequest from "../api/geology-url.api";
import { GeologyUrlBodyType } from "../model/schema/geology-url.schema";

export const useUpdateGeologyUrl = () => {
  const [loading, setLoading] = useState(false);
  async function request(
    params: GeologyUrlBodyType,
    onSuccess?: Function,
    onError?: Function,
  ) {
    setLoading(true);
    const response = await geologyUrlRequest.update(params);
    if (response.state === "success") {
      onSuccess?.(response.data);
      setLoading(false);
    } else {
      onError?.(response);
      setLoading(false);
    }
  }
  return {
    request,
    loading,
  };
};
