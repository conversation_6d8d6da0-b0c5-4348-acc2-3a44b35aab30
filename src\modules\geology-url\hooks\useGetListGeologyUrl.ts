import { useState } from "react";

import geologyUrlRequest from "../api/geology-url.api";
import { GeologyUrlQuery } from "../interface/geology-url.query";

export const useGetListGeologyUrl = () => {
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any[]>([]);
  const request = async (
    params: GeologyUrlQuery,
    onSuccess?: Function,
    onError?: Function,
  ) => {
    setLoading(true);
    const response = await geologyUrlRequest.getList({
      ...params,
      isActive: params.isActive ?? true,
    });
    if (response?.state === "success") {
      setData(response.data?.items);
      setTotal(response.data?.pagination?.total);
      setLoading(false);
      onSuccess?.(response.data);
      return response.data;
    } else {
      onError?.(response);
      setLoading(false);
      return null;
    }
  };

  return { request, loading, data, total };
};
