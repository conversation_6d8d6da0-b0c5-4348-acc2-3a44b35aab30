/* eslint-disable max-lines */
/* eslint-disable max-lines-per-function */
/* eslint-disable complexity */
/* eslint-disable react-hooks/exhaustive-deps */
import { Button, Form } from "antd";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-toastify";

import {
  useAppDispatch,
  useAppSelector,
} from "@/common/vendors/redux/store/hook";
import { InputNumberCommon } from "@/components/common/input-number";
import { FieldType } from "@/modules/geology-suite-field/const/enum";

import { DataEntryBody } from "../api/data-entry.api";
import { useCreateDataEntry } from "../hooks/useCreateDataEntry";
import { useUpdateDataEntry } from "../hooks/useUpdateDataEntry";
import {
  getLoggingBar,
  updateFetchDataEntry,
  updateRefetchLoggingView,
} from "../redux/loggingSlice";
import { RenderField } from "./renderField";

export interface IModalCreateEntryProps {
  modalState: {
    isOpen: boolean;
    detail?: DataEntryBody;
    type: "create" | "update" | "delete" | "view" | "update-bar";
  };
  setModalState: (value: {
    isOpen: boolean;
    detail?: DataEntryBody;
    type: "create" | "update" | "delete" | "view" | "update-bar";
  }) => void;
  refetchDataEntry: () => void;
}

export function ModalCreateEntry({
  modalState,
  setModalState,
  refetchDataEntry,
}: IModalCreateEntryProps) {
  const geologySuite = useAppSelector((state) => state.geologySuite.detail);
  const geologySuiteBar = useAppSelector(
    (state) => state.logging.geologySuiteBar,
  );
  const selectedImageTypeId = useAppSelector(
    (state) => state.logging.selectedImageTypeId,
  );
  const selectedImageSubtypeId = useAppSelector(
    (state) => state.logging.selectedImageSubtypeId,
  );

  // Use geologySuiteBar when type is "update-bar", otherwise use geologySuite
  const currentGeologySuite =
    modalState.type === "update-bar" ? geologySuiteBar : geologySuite;
  let geologySuiteFields = currentGeologySuite?.geologySuiteFields ?? [];
  geologySuiteFields = geologySuiteFields.filter((item) => item?.isActive);
  const dispatch = useAppDispatch();
  const { control, handleSubmit, setValue, getValues, reset } =
    useForm<DataEntryBody>();
  const { request: requestCreateDataEntry, loading: loadingCreateDataEntry } =
    useCreateDataEntry();
  const { detail: selectedDrillhole } = useAppSelector(
    (state) => state.drillHole,
  );
  const { detail: selectedGeologySuite } = useAppSelector(
    (state) => state.geologySuite,
  );
  const { request: requestUpdateDataEntry, loading: loadingUpdateDataEntry } =
    useUpdateDataEntry();
  const { allLoggings } = useAppSelector((state) => state.logging);
  const selectedImageSubTypeId = useAppSelector(
    (state) => state.logging.selectedImageSubtypeId,
  );
  const loggingBarSuite = useAppSelector(
    (state) => state.logging.loggingBarSuite,
  );

  // Refresh logging bar data if geology suite is selected
  const refreshLoggingBarData = () => {
    if (!loggingBarSuite || !selectedDrillhole?.id) {
      return;
    }
    if (Number(loggingBarSuite) !== Number(currentGeologySuite?.id)) {
      return;
    }

    const loggingBarParams: any = {
      GeologySuiteId: Number(loggingBarSuite),
      DrillholeId: Number(selectedDrillhole.id),
      ImageTypeId: selectedImageTypeId,
      ImageSubtypeId: selectedImageSubtypeId,
      SkipCount: 0,
      MaxResultCount: 1000,
    };

    // Include image subtype if available
    if (selectedImageSubTypeId) {
      loggingBarParams.ImageSubtypeId = selectedImageSubTypeId;
    }
    dispatch(getLoggingBar(loggingBarParams));
  };

  const onSubmit = (data: any) => {
    const checkIsInvalid = data.dataEntryValues.some((item) => {
      const geologySuiteField = geologySuiteFields.find(
        (field) => field.id === item.geologySuiteFieldId,
      );
      if (geologySuiteField?.isMandatory) {
        switch (item.fieldType) {
          case FieldType.Colour:
            if (!item.colourId) {
              toast.error("Colour is required");
              return true;
            }
            break;
          case FieldType.NumberField:
            if (!item.numberValue) {
              toast.error("Number Value is required");
              return true;
            }
            break;
          case FieldType.RockGroup:
            if (!item.rockTypeId) {
              toast.error("Rock Type is required");
              return true;
            }
            break;
          case FieldType.PickList:
            if (!item.pickListItemId) {
              toast.error("Pick List Item is required");
              return true;
            }
            break;
          case FieldType.Description:
            if (!item.description) {
              toast.error("Description is required");
              return true;
            }
            break;
          case FieldType.RockType:
            if (!item.numberValue) {
              toast.error("Number Value is required");
              return true;
            }
            break;
          case FieldType.RockSelect:
            if (!item.rockTypeId || !item.numberValue) {
              toast.error("Rock Type and Number Value are required");
              return true;
            }
            break;
          case FieldType.DateField:
            if (!item.dateValue) {
              toast.error("Date is required");
              return true;
            }
            break;
          case FieldType.Checkbox:
            if (
              item.checkBoxValue === undefined ||
              item.checkBoxValue === null
            ) {
              toast.error("Checkbox is required");
              return true;
            }
            break;
          case FieldType.Latitude:
            if (!item.numberValue) {
              toast.error("Latitude is required");
              return true;
            }
            break;
          case FieldType.Longitude:
            if (!item.numberValue) {
              toast.error("Longitude is required");
              return true;
            }
            break;
          case FieldType.Url:
            if (!item.url) {
              toast.error("URL is required");
              return true;
            }
            break;
          default:
            break;
        }
      }
      return false;
    });
    if (checkIsInvalid) {
      return;
    }

    const newDepthFrom = parseFloat(data.depthFrom?.toString() ?? "0");
    const newDepthTo = parseFloat(data.depthTo?.toString() ?? "0");

    if (newDepthFrom >= newDepthTo) {
      toast.error("Depth from must be less than depth to");
      return;
    }

    const hasOverlap = allLoggings.some((entry) => {
      const entryDepthFrom = parseFloat(entry.depthFrom?.toString() ?? "0");
      const entryDepthTo = parseFloat(entry.depthTo?.toString() ?? "0");

      if (modalState.detail && entry.id === modalState.detail.id) {
        return false;
      }

      return (
        (newDepthFrom >= entryDepthFrom && newDepthFrom < entryDepthTo) ||
        (newDepthTo > entryDepthFrom && newDepthTo <= entryDepthTo) ||
        (newDepthFrom <= entryDepthFrom && newDepthTo >= entryDepthTo)
      );
    });

    if (hasOverlap) {
      toast.error("Depth range overlaps with existing entries");
      return;
    }

    if (modalState.type !== "create") {
      requestUpdateDataEntry(
        {
          ...data,
          id: modalState.detail?.id,
          drillholeId: selectedDrillhole?.id,
          geologySuiteId:
            modalState.type === "update-bar"
              ? currentGeologySuite?.id
              : selectedGeologySuite?.id,
        },
        () => {
          reset();
          setModalState({ ...modalState, isOpen: false, detail: undefined });
          refetchDataEntry();
          dispatch(updateRefetchLoggingView());
          refreshLoggingBarData();

          toast.success("Update data entry success");
          dispatch(updateFetchDataEntry());
        },
        (error) => {
          toast.error(error?.message);
        },
      );
    } else {
      requestCreateDataEntry(
        {
          ...data,
          drillholeId: selectedDrillhole?.id,
          geologySuiteId: selectedGeologySuite?.id,
        },
        () => {
          reset();
          setModalState({ ...modalState, isOpen: false, detail: undefined });
          refetchDataEntry();
          dispatch(updateRefetchLoggingView());

          refreshLoggingBarData();
          toast.success("Create data entry success");
          dispatch(updateFetchDataEntry());
        },
        (error) => {
          toast.error(error?.message);
        },
      );
    }
  };

  useEffect(() => {
    // Determine which geology suite fields to use based on modal type
    const currentGeologySuiteFields =
      modalState.type === "update-bar"
        ? geologySuiteBar?.geologySuiteFields
        : geologySuiteFields;

    // Ensure we have the fields before proceeding
    if (!currentGeologySuiteFields || currentGeologySuiteFields.length === 0) {
      return;
    }

    if (
      modalState.detail &&
      (modalState.type === "update" || modalState.type === "update-bar")
    ) {
      setValue("depthFrom", modalState.detail?.depthFrom ?? undefined);
      setValue("depthTo", modalState.detail?.depthTo ?? undefined);
      const _dataEntries: any[] = [];

      currentGeologySuiteFields.forEach((field) => {
        const isNumberField =
          field?.geologyField?.type === FieldType.NumberField;
        const isRockGroupField =
          field?.geologyField?.type === FieldType.RockGroup;
        const isPickField = field?.geologyField?.type === FieldType.PickList;
        const isDescriptionField =
          field?.geologyField?.type === FieldType.Description;
        const isRockTypeField =
          field?.geologyField?.type === FieldType.RockType;
        const isRockSelectField =
          field?.geologyField?.type === FieldType.RockSelect;
        const isDateField = field?.geologyField?.type === FieldType.DateField;
        const isColourField = field?.geologyField?.type === FieldType.Colour;
        const isRockTreeField =
          field?.geologyField?.type === FieldType.RockTree;
        const isCheckboxField =
          field?.geologyField?.type === FieldType.Checkbox;
        const isLatitudeField =
          field?.geologyField?.type === FieldType.Latitude;
        const isLongitudeField =
          field?.geologyField?.type === FieldType.Longitude;
        const isUrlField = field?.geologyField?.type === FieldType.Url;
        if (isColourField) {
          const item = modalState?.detail?.dataEntryValues?.find(
            (value) => value.geologysuiteFieldId === field.id,
          );
          if (item) {
            _dataEntries.push({
              fieldType: FieldType.Colour,
              geologySuiteFieldId: field.id,
              colourId: item?.colourId,
            });
          }
        } else if (isNumberField) {
          const item = modalState?.detail?.dataEntryValues?.find(
            (value) => value.geologysuiteFieldId === field.id,
          );
          if (item) {
            _dataEntries.push({
              fieldType: FieldType.NumberField,
              geologySuiteFieldId: field.id,
              numberValue: item?.numberValue,
              numberId: field?.geologyField?.number?.id,
            });
          }
        } else if (isRockGroupField) {
          const item = modalState?.detail?.dataEntryValues?.find(
            (value) => value.geologysuiteFieldId === field.id,
          );
          if (item) {
            _dataEntries.push({
              fieldType: FieldType.RockGroup,
              geologySuiteFieldId: field.id,
              rockTypeId: item?.rockTypeId,
            });
          }
        } else if (isPickField) {
          const item = modalState?.detail?.dataEntryValues?.find(
            (value) => value.geologysuiteFieldId === field.id,
          );

          if (item) {
            _dataEntries.push({
              fieldType: FieldType.PickList,
              geologySuiteFieldId: field.id,
              pickListItemId: item?.pickListItemId,
            });
          }
        } else if (isDescriptionField) {
          const item = modalState?.detail?.dataEntryValues?.find(
            (value) => value.geologysuiteFieldId === field.id,
          );
          if (item) {
            _dataEntries.push({
              fieldType: FieldType.Description,
              geologySuiteFieldId: field.id,
              description: item?.description,
            });
          }
        } else if (isRockTypeField) {
          const item = modalState?.detail?.dataEntryValues?.find(
            (value) => value.geologysuiteFieldId === field.id,
          );
          if (item) {
            _dataEntries.push({
              fieldType: FieldType.RockType,
              geologySuiteFieldId: field.id,
              numberValue: item?.numberValue,
              numberId: field?.geologyField?.rockTypeNumber?.number?.id,
            });
          }
        } else if (isRockSelectField) {
          const item = modalState?.detail?.dataEntryValues?.find(
            (value) => value.geologysuiteFieldId === field.id,
          );
          if (item) {
            _dataEntries.push({
              fieldType: FieldType.RockSelect,
              geologySuiteFieldId: field.id,
              rockTypeId: item?.rockTypeId,
              numberValue: item?.numberValue,
              numberId: field?.geologyField?.rockSelectNumber?.number?.id,
            });
          }
        } else if (isDateField) {
          const item = modalState?.detail?.dataEntryValues?.find(
            (value) => value.geologysuiteFieldId === field.id,
          );

          if (item) {
            _dataEntries.push({
              fieldType: FieldType.DateField,
              geologySuiteFieldId: field.id,
              dateValue: item?.dateValue?.toString(),
            });
          }
        } else if (isRockTreeField) {
          const item = modalState?.detail?.dataEntryValues?.find(
            (value) => value.geologysuiteFieldId === field.id,
          );
          if (item) {
            _dataEntries.push({
              fieldType: FieldType.RockTree,
              geologySuiteFieldId: field.id,
              rockNodeId: item?.rockNodeId,
            });
          }
        } else if (isCheckboxField) {
          const item = modalState?.detail?.dataEntryValues?.find(
            (value) => value.geologysuiteFieldId === field.id,
          );

          if (item) {
            _dataEntries.push({
              fieldType: FieldType.Checkbox,
              geologySuiteFieldId: field.id,
              checkBoxValue: item?.checkBoxValue ?? false,
            });
          }
        } else if (isLatitudeField) {
          const item = modalState?.detail?.dataEntryValues?.find(
            (value) => value.geologysuiteFieldId === field.id,
          );

          if (item) {
            _dataEntries.push({
              fieldType: FieldType.Latitude,
              geologySuiteFieldId: field.id,
              numberValue: item?.numberValue,
            });
          }
        } else if (isLongitudeField) {
          const item = modalState?.detail?.dataEntryValues?.find(
            (value) => value.geologysuiteFieldId === field.id,
          );
          if (item) {
            _dataEntries.push({
              fieldType: FieldType.Longitude,
              geologySuiteFieldId: field.id,
              numberValue: item?.numberValue,
            });
          }
        } else if (isUrlField) {
          const item = modalState?.detail?.dataEntryValues?.find(
            (value) => value.geologysuiteFieldId === field.id,
          );
          if (item) {
            _dataEntries.push({
              fieldType: FieldType.Url,
              geologySuiteFieldId: field.id,
              url: item?.url,
            });
          }
        }
      });
      setValue("dataEntryValues", _dataEntries);
    } else {
      reset();
      const _dataEntries: any[] = [];
      currentGeologySuiteFields.forEach((field) => {
        const baseEntry = {
          fieldType: field.geologyField?.type,
          geologySuiteFieldId: field.id,
        };

        // Add specific properties based on field type
        if (field.geologyField?.type === FieldType.Url) {
          _dataEntries.push({
            ...baseEntry,
            url: "",
          });
        } else if (field.geologyField?.type === FieldType.Checkbox) {
          _dataEntries.push({
            ...baseEntry,
            checkboxValue: false,
          });
        } else if (
          field.geologyField?.type === FieldType.Latitude ||
          field.geologyField?.type === FieldType.Longitude
        ) {
          _dataEntries.push({
            ...baseEntry,
            numberValue: "",
          });
        } else {
          _dataEntries.push(baseEntry);
        }
      });
      setValue("dataEntryValues", _dataEntries);
    }
  }, [
    modalState.detail,
    modalState.type,
    currentGeologySuite,
    geologySuiteBar,
    setValue,
    reset,
  ]);

  const logging = useAppSelector((state) => state.logging);
  const { reviewDepthFrom, reviewDepthTo } = logging;
  useEffect(() => {
    if (!modalState.detail) {
      setValue("depthFrom", reviewDepthFrom ?? undefined);
      setValue("depthTo", reviewDepthTo ?? undefined);
    } else {
      // When modalState.detail exists, use the values from detail
      setValue("depthFrom", modalState.detail.depthFrom ?? undefined);
      setValue("depthTo", modalState.detail.depthTo ?? undefined);
    }
  }, [modalState, reviewDepthFrom, reviewDepthTo, geologySuiteBar, setValue]);
  return (
    <Form onFinish={handleSubmit(onSubmit)}>
      <div className="flex flex-col gap-2">
        <p className="font-semibold text-32-32">
          {modalState.type === "create" ? "Add entry" : "Update entry"}
        </p>
        <div className="flex gap-2">
          <p className="font-semibold">Drillhole</p>
          <p>{selectedDrillhole?.name}</p>
        </div>
        <div className="flex gap-2">
          <p className="font-semibold">Geology Suite</p>
          <p>{currentGeologySuite?.name}</p>
        </div>
        <div className="flex flex-col gap-4 ">
          <InputNumberCommon
            control={control}
            name="depthFrom"
            className="w-24"
            label="Depth From"
            placeholder="Depth From"
            precision={2}
          />
          <InputNumberCommon
            control={control}
            name="depthTo"
            className="w-24"
            label="Depth To"
            placeholder="Depth To"
            precision={2}
          />
        </div>
        <div className="flex flex-col gap-4 max-h-[500px] overflow-y-auto">
          {(modalState.type === "update-bar"
            ? geologySuiteBar?.geologySuiteFields
            : geologySuiteFields
          )
            ?.filter((field) => field.isActive)
            .map((field, index) => (
              <div
                key={field.id}
                style={{
                  backgroundColor: field.backgroundColour,
                  color: field.textColour,
                }}
                className="flex flex-col gap-2 rounded-lg border border-black p-2"
              >
                <p className="font-semibold uppercase">
                  {field.name} {field.isMandatory ? "*" : ""}
                </p>
                <RenderField
                  field={field}
                  control={control}
                  setValue={setValue}
                  getValues={getValues}
                  index={index}
                />
              </div>
            ))}
        </div>
        <div className="flex justify-end gap-2 ">
          <Button
            onClick={() => setModalState({ ...modalState, isOpen: false })}
          >
            Cancel
          </Button>
          <Button
            type="primary"
            htmlType="submit"
            loading={loadingUpdateDataEntry || loadingCreateDataEntry}
          >
            {modalState.type === "create" ? "Save" : "Update"}
          </Button>
        </div>
      </div>
    </Form>
  );
}
