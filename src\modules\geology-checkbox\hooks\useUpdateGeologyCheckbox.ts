import { useState } from "react";

import geologyCheckboxRequest from "../api/geology-checkbox.api";
import { GeologyCheckboxBodyType } from "../model/schema/geology-checkbox.schema";

export const useUpdateGeologyCheckbox = () => {
  const [loading, setLoading] = useState(false);
  async function request(
    params: GeologyCheckboxBodyType,
    onSuccess?: Function,
    onError?: Function,
  ) {
    setLoading(true);
    const response = await geologyCheckboxRequest.update(params);
    if (response.state === "success") {
      onSuccess?.(response.data);
      setLoading(false);
    } else {
      onError?.(response);
      setLoading(false);
    }
  }
  return {
    request,
    loading,
  };
};
