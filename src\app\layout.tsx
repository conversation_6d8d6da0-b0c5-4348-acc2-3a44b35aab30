import "react-toastify/dist/ReactToastify.css";

import { Metadata } from "next";
import { DM_Sans } from "next/font/google";

import { StoreProvider } from "@/common/vendors/redux/provider/StoreProvider";
export const metadata: Metadata = {
  title: "FastGeo - A high performance platform for custom AI solutions",
  description: "innovation driven performance",
};
const dmSans = DM_Sans({
  subsets: ["latin"],
  weight: ["400", "500", "700"],
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={dmSans.className}>
      <link rel="icon" href="/favicon.png" sizes="any" />
      <body className="font-dmSans bg-[#f5f5f5]">
        <StoreProvider>{children}</StoreProvider>
      </body>
    </html>
  );
}
