import { Control, UseFormGetValues, UseFormSetValue } from "react-hook-form";

import { InputNumberCommon } from "../../../../components/common/input-number";
import { DataEntryBody } from "../../api/data-entry.api";

interface RenderLatitudeFieldProps {
  field: any;
  control: Control<DataEntryBody>;
  index: number;
  setValue: UseFormSetValue<DataEntryBody>;
  getValues: UseFormGetValues<DataEntryBody>;
}

export default function RenderLatitudeField({
  field,
  control,
  index,
  setValue,
  getValues,
}: RenderLatitudeFieldProps) {
  return (
    <InputNumberCommon
      control={control}
      name={`dataEntryValues.${index}.numberValue`}
      placeholder={`Enter ${field?.geologyField?.name}`}
      onChange={(value) => {
        const currentValues = getValues("dataEntryValues") || [];
        const newValues = [...currentValues];
        newValues[index] = {
          numberValue: value ?? undefined,
          geologysuiteFieldId: field.id,
          fieldType: field?.geologyField?.type,
        };
        const filteredValues = newValues.filter(
          (value) => value && Object.keys(value).length > 0,
        );
        setValue("dataEntryValues", filteredValues);
      }}
    />
  );
}
