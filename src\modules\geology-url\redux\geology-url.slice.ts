import { createSlice } from "@reduxjs/toolkit";

import { RequestState } from "@/common/configs/app.contants";

import { getDetailGeologyUrl, getListGeologyUrl } from "./thunks";

const initialState: GeologyUrlSliceState = {
  status: RequestState.idle,
  getDetailStatus: RequestState.idle,
};

export const geologyUrlSlice = createSlice({
  name: "geologyUrl",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getListGeologyUrl.pending, (state) => {
        state.status = RequestState.pending;
      })
      .addCase(getListGeologyUrl.fulfilled, (state, action) => {
        state.status = RequestState.success;
        state.result = action.payload.data;
      })
      .addCase(getDetailGeologyUrl.pending, (state) => {
        state.getDetailStatus = RequestState.pending;
      })
      .addCase(getDetailGeologyUrl.fulfilled, (state, action) => {
        state.getDetailStatus = RequestState.success;
        state.detail = action.payload;
      });
  },
});

export interface GeologyUrlSliceState {
  result?: any;
  status: RequestState;
  getDetailStatus: RequestState;
  detail?: any;
}

export const {} = geologyUrlSlice.actions;
