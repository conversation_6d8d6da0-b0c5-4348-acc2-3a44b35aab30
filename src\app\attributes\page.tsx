import PermissionProvider from "@/components/layout/permission-provider";
import { PERMISSIONS } from "@/constants/general.const";
import TableAttribute from "@/modules/downhole-point/components/attributes/table-attribute";

export default function Page() {
  return (
    <PermissionProvider
      permissions={[
        PERMISSIONS.Admin,
        PERMISSIONS.Company,
        PERMISSIONS.EditorUser,
        PERMISSIONS.ViewerUser,
      ]}
    >
      <TableAttribute />
    </PermissionProvider>
  );
}
