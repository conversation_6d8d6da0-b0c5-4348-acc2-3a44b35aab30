import { Control, UseFormGetValues, UseFormSetValue } from "react-hook-form";

import { TextAreaCommon } from "@/components/common/textarea-common";

import { DataEntryBody } from "../../api/data-entry.api";
export default function RenderDescription(props: {
  field: any;
  control: Control<DataEntryBody>;
  index: number;
  setValue: UseFormSetValue<DataEntryBody>;
  getValues: UseFormGetValues<DataEntryBody>;
}) {
  const { field, control, index, setValue, getValues } = props;

  return (
    <>
      <TextAreaCommon
        control={control}
        name={`dataEntryValues.${index}.description`}
        placeholder={`Enter ${field?.geologyField?.name}`}
        rows={field?.geologyField?.geologyDescription?.fieldHeight}
        onChange={(value) => {
          const currentValues = getValues("dataEntryValues") || [];
          const newValues = [...currentValues];
          newValues[index] = {
            description: value.target.value ?? undefined,
            geologysuiteFieldId: field.id,
            fieldType: field?.geologyField?.type,
          };
          const filteredValues = newValues.filter(
            (value) => value && Object.keys(value).length > 0,
          );
          setValue("dataEntryValues", filteredValues);
        }}
      />
    </>
  );
}
