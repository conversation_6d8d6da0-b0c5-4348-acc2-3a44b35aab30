.high-performance-table .ant-table-tbody > tr > td {
  padding: 4px 8px;
  vertical-align: top;
}

.high-performance-table .ant-table-thead > tr > th {
  padding: 8px;
  font-weight: 600;
  background-color: #fafafa;
}

/* Error state styling */
.high-performance-table .ant-input.error,
.high-performance-table .ant-input-number.error {
  border-color: #ff4d4f;
}

/* Loading state for rows */
.high-performance-table .loading-row {
  opacity: 0.6;
  pointer-events: none;
}

/* Enhanced smooth scrolling with performance optimizations */
.table-virtualized::-webkit-scrollbar,
.high-performance-table-body::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  display: none;
}

.table-virtualized::-webkit-scrollbar-track,
.high-performance-table-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.table-virtualized::-webkit-scrollbar-thumb,
.high-performance-table-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.table-virtualized::-webkit-scrollbar-thumb:hover,
.high-performance-table-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.high-performance-table .ant-table-body {
  height: calc(100% - 55px);
  /* Account for header height */
  overflow: auto !important;
}

.ant-tabs-content.ant-tabs-content-top {
  height: 100%;
}

/* Prevent input field clipping during horizontal scroll */
.virtualized-table-body .ant-select-dropdown {
  z-index: 1050;
  /* Ensure dropdowns appear above other elements */
}

/* Smooth horizontal scrolling */
.virtualized-table-body {
  scroll-behavior: smooth;
}

/* Ensure table header cells do not wrap text */
.virtualized-table-header > div > div {
  white-space: nowrap;
}

/* Styles for fixed columns */
.fixed-column {
  position: sticky !important;
  /* Ensure stickiness */
  z-index: 10;
  /* Higher z-index for fixed columns */
  background-color: white;
  /* Or your table's row background color */
  box-shadow: 1px 0 0 0 rgba(0, 0, 0, 0.06);
  left: 0;
}

.fixed-column-header {
  position: sticky !important;
  /* Ensure stickiness */
  z-index: 20;
  /* Higher z-index for fixed headers */
  background-color: #f9fafb;
  /* Match header background color */
  /* Ensure fixed headers stay above everything */
  box-shadow: 1px 0 0 0 rgba(0, 0, 0, 0.06);
}

/* Visual separator for the last fixed column */
.last-fixed-column {
  border-right: 2px solid #d1d5db !important;
  /* Stronger visual separator */
  box-shadow: 2px 0 4px -1px rgba(0, 0, 0, 0.1) !important;
  /* Subtle shadow for depth */
}

/* Ensure fixed columns maintain background on hover */
.fixed-column:hover {
  background-color: #f9fafb !important;
}

/* Ensure fixed columns maintain proper borders during scroll */
.virtualized-table-body .fixed-column {
  border-right: 1px solid #e5e7eb;
}

/* Ensure last fixed column separator is always visible */
.last-fixed-column::after {
  content: "";
  position: absolute;
  top: 0;
  right: -1px;
  bottom: 0;
  width: 2px;
  background-color: #d1d5db;
  pointer-events: none;
}

/* Virtualized table cell content styling for fixed height constraints */
.virtualized-table-row-cell {
  box-sizing: border-box;
  overflow: hidden !important;
  /* Strict overflow control */
  position: relative;
}

.virtualized-cell-content {
  width: 100%;
  height: 100%;
  max-height: 56px;
  /* Account for cell padding (60px - 4px padding) */
  display: flex;
  align-items: center;
  justify-content: flex-start;
  overflow: hidden;
  box-sizing: border-box;
  position: relative;
}

/* Ensure form controls fit within fixed cell height */
.virtualized-cell-content .ant-input,
.virtualized-cell-content .ant-input-number,
.virtualized-cell-content .ant-select,
.virtualized-cell-content .ant-picker {
  width: 100% !important;
  min-width: 0 !important;
  max-width: 100% !important;
  height: 36px !important;
  /* Fixed height for all form controls */
  max-height: 36px !important;
  box-sizing: border-box;
  overflow: hidden;
}

/* Handle textarea specifically - single line with ellipsis */
.virtualized-cell-content .ant-input {
  resize: none !important;
  overflow: hidden !important;
  white-space: nowrap !important;
  text-overflow: ellipsis !important;
  height: 36px !important;
  max-height: 36px !important;
  line-height: 34px !important;
  /* Account for border */
  padding: 0 8px !important;
}

/* Input number specific styling */
.virtualized-cell-content .ant-input-number {
  height: 36px !important;
  max-height: 36px !important;
}

.virtualized-cell-content .ant-input-number-input {
  height: 34px !important;
  line-height: 34px !important;
  padding: 0 8px !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* Select component styling */
.virtualized-cell-content .ant-select-selector {
  height: 36px !important;
  max-height: 36px !important;
  overflow: hidden !important;
}

.virtualized-cell-content .ant-select-selection-item {
  line-height: 34px !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}

/* Ensure select dropdowns don't get clipped */
.virtualized-cell-content .ant-select-dropdown {
  z-index: 1060 !important;
}

/* Ensure proper alignment for cell content with units */
.virtualized-cell-content .flex {
  width: 100%;
  min-width: 0;
  height: 100%;
}

/* Handle error messages within cells - compact display */
.virtualized-cell-content .text-red-500 {
  font-size: 9px;
  line-height: 1;
  color: #ff4d4f;
  z-index: 10;
  text-overflow: ellipsis;
  white-space: nowrap;
  position: absolute;
  bottom: -2px;
  left: 0px;
}

/* Ensure buttons in action cells fit properly */
.virtualized-cell-content .ant-btn {
  margin: 1px;
  padding: 4px 8px;
  font-size: 12px;
  line-height: 1.2;
  height: 32px !important;
  max-height: 32px !important;
  min-height: 32px !important;
}

/* Handle loading states */
.virtualized-cell-content .ant-spin {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 36px;
}

/* Handle units display in number fields */
.virtualized-cell-content .text-gray-600 {
  font-size: 12px;
  line-height: 36px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 40px;
}

/* Ensure dropdowns have proper z-index and positioning */
.ant-select-dropdown,
.ant-picker-dropdown {
  z-index: 1070 !important;
}

/* Strict overflow control for all cell content */
.virtualized-table-row-cell,
.virtualized-table-row-cell:hover {
  overflow: hidden !important;
}

/* Ensure fixed columns maintain strict overflow control */
.fixed-column,
.fixed-column:hover {
  overflow: hidden !important;
}

/* Tooltip support for truncated content */
.virtualized-cell-content[title] {
  cursor: help;
}

/* Unified MultiGrid header styles */
.header-cell {
  user-select: none;
  /* Prevent text selection in headers */
  cursor: default;
}

.header-cell:hover {
  background-color: #e5e7eb !important;
  /* Hover effect for headers */
}

/* Enhanced fixed column header styling for unified approach */
.fixed-column-header {
  background-color: #f3f4f6 !important;
  box-shadow: 1px 0 0 0 rgba(0, 0, 0, 0.06);
}

.fixed-column-header:hover {
  background-color: #e5e7eb !important;
}

/* Last fixed column separator for unified approach */
.last-fixed-column {
  border-right: 2px solid #d1d5db !important;
  box-shadow: 2px 0 4px -1px rgba(0, 0, 0, 0.1) !important;
}

/* Vertical-only virtualization container styles */
.vertical-only-virtualized-container {
  /* Ensure smooth horizontal scrolling when needed */
  scroll-behavior: smooth;
}

.vertical-only-virtualized-container::-webkit-scrollbar {
  height: 8px;
}

.vertical-only-virtualized-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.vertical-only-virtualized-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.vertical-only-virtualized-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.ReactVirtualized__Grid::-webkit-scrollbar {
  display: none;
}

.scrolling-disabled {
  overflow: hidden !important;
  pointer-events: none;
}
