import { notification } from "antd";

function useCopyText() {
  const copyText = async (text, successMessage = "Copied successfully") => {
    try {
      await navigator.clipboard.writeText(text);
      notification.success({
        message: successMessage,
      });
    } catch {
      notification.error({
        message: "An error occurs",
      });
    }
  };

  return copyText;
}

export default useCopyText;
