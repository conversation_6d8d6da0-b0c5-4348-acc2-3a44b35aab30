/* eslint-disable simple-import-sort/imports */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable max-lines */
/* eslint-disable max-lines-per-function */
/* eslint-disable complexity */
"use client";
import {
  Button,
  Dropdown,
  Image,
  Input,
  InputRef,
  Select,
  Slider,
  Tooltip,
} from "antd";
import { Layer as KonvaLayer } from "konva/lib/Layer";
import { Stage as KonvaStage } from "konva/lib/Stage";
import { isEmpty, isNumber } from "lodash";
// Added for URL updates
import { memo, useCallback, useEffect, useRef, useState } from "react";
import { FaArrowLeft, FaArrowRight } from "react-icons/fa6";
import {
  LuAlignHorizontalJustifyCenter,
  LuAlignHorizontalSpaceBetween,
} from "react-icons/lu";
import { PiResizeLight } from "react-icons/pi";
import { RiInformationLine, RiInformationOffLine } from "react-icons/ri";
import { RxText, RxTextNone } from "react-icons/rx";
import { Layer, Rect, Stage } from "react-konva";
import { AutoSizer } from "react-virtualized";

import {
  useAppDispatch,
  useAppSelector,
} from "@/common/vendors/redux/store/hook";
import { ButtonCommon } from "@/components/common/button-common";

import { defaultDrillHoleViewConfig } from "../const/drillhole-view.const";
import { EnumDrillholeView } from "../model/enum/drillhole.enum";
import {
  ImageSizeEnum,
  ImageSizeOptions,
  ImageView,
} from "../model/enum/images.enum";
import {
  selectAttributePoints,
  selectDHViewConfig,
  selectImages,
  updateDHViewConfigs,
  updateDHViewInfo,
  updatePreviewSize,
} from "../redux/imageSlice";
import VerticalRuler from "./canvas/vertical-ruler";
import DrillHolePanel from "./drillhole-view-panel";
import DrillHoleViewStacks from "./drillhole-view.stack";

export interface DrillHoleImages {
  list: any[];
  drillHole: any;
  minHeight?: number;
  maxHeight?: number;
  maxWidth?: number;
  type?: number;
  subType?: number | null;
  typeName?: string;
  subTypeName?: string;
}

const AutoSizerTmp: any = AutoSizer;

function DrillHoleView({ alignValue }: { alignValue: ImageView }) {
  const imagesOfDrillHoles = useAppSelector(selectImages).imagesOfDrillHoles;

  const layerRef = useRef<KonvaLayer>(null);
  const stageRef = useRef<KonvaStage>(null);
  const goToDepthRef = useRef<InputRef>(null);
  const [currentPosLayer, setCurrentPosLayer] = useState({ x: 0, y: 0 });
  const dHViewInfo = useAppSelector(selectImages)?.dHViewInfo;
  const rulerSpacing = useAppSelector(selectImages)?.rulerSpacing ?? 1;
  const dHAttributePoints = useAppSelector(selectAttributePoints) ?? [];
  const dhSuitesLogging =
    useAppSelector((state) => state.images.multiLoggings) ?? {};

  const previewImageSize =
    useAppSelector(selectImages)?.previewSize ?? ImageSizeEnum.MEDIUM;

  const [currentScale, setCurrentScale] = useState(1);
  const [goToDepth, setGotoDepth] = useState("");

  const drillHoleViewMode = useAppSelector(selectImages).drillholeViewMode;
  const dHViewConfigs = useAppSelector(selectDHViewConfig);
  const previewImage = useAppSelector(selectImages)?.dHViewInfo?.urlPreview;
  const dispatch = useAppDispatch();
  const [lastCenter, setLastCenter] = useState<{ x: number; y: number } | null>(
    null,
  );
  const [lastDist, setLastDist] = useState<number>(0);
  const [isZooming, setIsZooming] = useState(false);
  const [initialDepthFromUrl, setInitialDepthFromUrl] = useState<number | null>(
    null,
  );

  // Use Redux state for selected hole names
  const selectedHoleNames = useAppSelector(
    (state) => state.images.selectedHoleNames,
  );

  const animationFrameRef = useRef<number>();
  const touchStartTime = useRef<number>(0);
  // Store initial touch center in layer coordinates
  const initialTouchCenterRef = useRef<{ x: number; y: number } | null>(null);

  const getDistance = (
    p1: { x: number; y: number },
    p2: { x: number; y: number },
  ) => {
    return Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2));
  };

  const getCenter = (
    p1: { x: number; y: number },
    p2: { x: number; y: number },
  ) => {
    return {
      x: (p1.x + p2.x) / 2,
      y: (p1.y + p2.y) / 2,
    };
  };

  const handleWheel = (e: any) => {
    e.evt.preventDefault();
    const stage = stageRef.current;
    const layer = layerRef.current;
    if (!layer || !stage) {
      return;
    }

    const oldScale = layer.scaleX();
    const pointer = stage.getPointerPosition();
    if (!pointer) {
      return;
    }

    const mousePointTo = {
      x: (pointer.x - layer.x()) / oldScale,
      y: (pointer.y - layer.y()) / oldScale,
    };

    const newScale =
      e.evt.deltaY > 0
        ? Math.max(oldScale / 1.05, dHViewConfigs?.minScale)
        : Math.min(oldScale * 1.05, dHViewConfigs?.maxScale);

    layer.scale({ x: newScale, y: newScale });
    setCurrentScale(Number((newScale / dHViewConfigs?.initScale).toFixed(2)));

    const newPos = {
      x: pointer.x - mousePointTo.x * newScale,
      y: pointer.y - mousePointTo.y * newScale,
    };

    layer.position(newPos);
    layer.batchDraw();
  };

  const handleTouchStart = (e: any) => {
    e.evt.preventDefault();
    const touches = e.evt.touches;
    touchStartTime.current = Date.now();

    if (touches.length === 2) {
      // Two-finger gesture starting - initiate zoom mode
      setIsZooming(true);

      const stage = stageRef.current;
      const layer = layerRef.current;
      if (!layer || !stage) {
        return;
      }

      // Get touch points in screen coordinates
      const p1 = {
        x: touches[0].clientX,
        y: touches[0].clientY,
      };
      const p2 = {
        x: touches[1].clientX,
        y: touches[1].clientY,
      };

      // Calculate center between touches in screen coordinates
      const center = getCenter(p1, p2);
      const dist = getDistance(p1, p2);

      // Get the stage container position and dimensions
      const stageBox = stage.container().getBoundingClientRect();

      // Convert screen coordinates to stage coordinates
      const stagePoint = {
        x: center.x - stageBox.left,
        y: center.y - stageBox.top,
      };

      // Calculate center point in layer's coordinate system
      const oldScale = layer.scaleX();
      const centerPointTo = {
        x: (stagePoint.x - layer.x()) / oldScale,
        y: (stagePoint.y - layer.y()) / oldScale,
      };

      // Store this point for use during zoom
      initialTouchCenterRef.current = centerPointTo;

      // Set last center and distance for subsequent move events
      setLastCenter(center);
      setLastDist(dist);

      // Pre-calculate and store initial scale for smoother transitions
      layer.setAttr("lastScale", oldScale);
    } else if (touches.length === 1) {
      // Single-finger gesture - prepare for potential drag
      setIsZooming(false);
    }
  };

  const handleTouchMove = (e: any) => {
    e.evt.preventDefault();
    const touches = e.evt.touches;

    if (touches.length !== 2) {
      return;
    }

    const stage = stageRef.current;
    const layer = layerRef.current;
    if (
      !layer ||
      !stage ||
      !lastCenter ||
      !lastDist ||
      !initialTouchCenterRef.current
    ) {
      return;
    }

    // Get touch points in screen coordinates
    const p1 = {
      x: touches[0].clientX,
      y: touches[0].clientY,
    };
    const p2 = {
      x: touches[1].clientX,
      y: touches[1].clientY,
    };

    const dist = getDistance(p1, p2);
    const currentScale = layer.scaleX();

    // Calculate current center in screen coordinates
    const currentCenter = getCenter(p1, p2);

    // Get the stage container position
    const stageBox = stage.container().getBoundingClientRect();

    // Convert screen coordinates to stage coordinates
    const stagePoint = {
      x: currentCenter.x - stageBox.left,
      y: currentCenter.y - stageBox.top,
    };

    // Calculate scale factor with reduced sensitivity for more controlled zooming
    const scaleFactor = Math.pow(dist / lastDist, 1.2); // Reduced scaling power
    const zoomSpeed = 1.03; // Reduced zoom speed multiplier
    const targetScale = Math.min(
      Math.max(currentScale * scaleFactor * zoomSpeed, dHViewConfigs?.minScale),
      dHViewConfigs?.maxScale,
    );

    // Cancel any ongoing animation
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }

    // Apply new scale
    layer.scale({ x: targetScale, y: targetScale });
    setCurrentScale(
      Number((targetScale / dHViewConfigs?.initScale).toFixed(2)),
    );

    // Calculate new position to maintain the initial touch center point
    const newPos = {
      x: stagePoint.x - initialTouchCenterRef.current.x * targetScale,
      y: stagePoint.y - initialTouchCenterRef.current.y * targetScale,
    };

    // Apply new position to maintain zoom center
    layer.position(newPos);
    layer.batchDraw();

    // Update state for next move
    setLastDist(dist);
    setLastCenter(currentCenter);
    setIsZooming(true);
  };

  const handleTouchEnd = (e: any) => {
    e.evt.preventDefault();
    const layer = layerRef.current;

    if (e.evt.touches.length < 2 && layer) {
      // Reset touch center reference when touch ends
      initialTouchCenterRef.current = null;

      // Reset touch state
      setLastCenter(null);
      setLastDist(0);
      setIsZooming(false);

      // // Only reset dragging if the touch duration was short (to prevent accidental drag cancellation)
      // if (touchDuration < 300) {
      //   setIsDragging(false);
      // }
    }

    if (e.evt.touches.length === 0) {
      // All touches ended
      setIsZooming(false);
      touchStartTime.current = 0;
    }
  };

  const handleMouseMove = () => {
    if (layerRef.current) {
      const layer = layerRef.current;
      const pointerPosition = layer.getStage().getPointerPosition();
      if (pointerPosition) {
        const transform = layer.getAbsoluteTransform().copy();
        transform.invert();
        const pos = transform.point(pointerPosition);
        setCurrentPosLayer({
          x: parseFloat(pos.x.toFixed(2)),
          y: parseFloat(pos.y.toFixed(2)),
        });
      }
    }
  };

  const handlePreviewVisibleChange = (visible: any) => {
    if (!visible) {
      dispatch(updateDHViewInfo({ urlPreview: "" }));
    }
  };

  const getOffsetY = () => {
    return dHViewInfo.minHeight ?? 0;
  };

  const reset = () => {
    const offsetY = getOffsetY();
    if (dHViewInfo?.minHeight) {
      layerRef.current?.offsetY(offsetY);
      layerRef.current?.y(100);
    }
    layerRef.current?.offsetX(0);
    layerRef.current?.x(0);
    layerRef.current?.scale({
      x: dHViewConfigs?.initScale,
      y: dHViewConfigs?.initScale,
    });
    setCurrentScale(1);
    layerRef.current?.batchDraw();
  };

  const goToYPosition = useCallback((y: number) => {
    if (layerRef.current) {
      layerRef.current.offsetY(y);
      layerRef.current.batchDraw();
    }
  }, []);

  // Track previous values to detect layout-affecting changes
  const prevRulerSpacingRef = useRef(rulerSpacing);
  const isInitialMountRef = useRef(true);

  useEffect(() => {
    const rulerSpacingChanged = prevRulerSpacingRef.current !== rulerSpacing;

    // Reset on initial mount or when layout-affecting changes occur
    // Don't reset for geology data changes (minHeight/maxHeight changes alone)
    if (isInitialMountRef.current || rulerSpacingChanged) {
      reset();
    }

    prevRulerSpacingRef.current = rulerSpacing;
    isInitialMountRef.current = false;
  }, [
    drillHoleViewMode,
    dHViewInfo?.maxHeight,
    dHViewInfo?.minHeight,
    rulerSpacing,
  ]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (
        !layerRef.current ||
        !dHViewInfo?.minHeight ||
        !dHViewInfo?.maxHeight
      ) {
        return;
      }

      const currentOffsetY = layerRef.current.offsetY();
      const pageSize = 10; // 80% of view height for page up/down

      if (e.key === "PageDown" || e.key === "ArrowDown") {
        // Move view down (increase offset)
        const newOffsetY = Math.min(
          currentOffsetY + pageSize,
          dHViewInfo.maxHeight,
        );
        goToYPosition(newOffsetY);
      } else if (e.key === "PageUp" || e.key === "ArrowUp") {
        // Move view up (decrease offset)
        const newOffsetY = Math.max(
          currentOffsetY - pageSize,
          dHViewInfo.minHeight,
        );
        goToYPosition(newOffsetY);
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
      // dispatch(updateDrillholeView(EnumDrillholeView.NoChoice));
      // dispatch(updateAttributePoints([]));
    };
  }, [dHViewInfo?.minHeight, dHViewInfo?.maxHeight, goToYPosition]);

  const [isPanelVisible, setIsPanelVisible] = useState(true);

  const togglePanel = () => {
    setIsPanelVisible(!isPanelVisible);
  };

  const handleDepthParamChangeCallback = useCallback((depth: number | null) => {
    if (depth !== null) {
      setGotoDepth(depth.toString()); // Update input field
      setInitialDepthFromUrl(depth); // Trigger scroll
    } else {
      // If depth param is removed or invalid from URL, clear the input
      setGotoDepth("");
    }
  }, []);

  useEffect(() => {
    if (
      initialDepthFromUrl !== null &&
      layerRef.current &&
      isNumber(dHViewInfo.minHeight) &&
      isNumber(dHViewInfo.maxHeight)
    ) {
      goToYPosition(initialDepthFromUrl);
      setInitialDepthFromUrl(null); // Reset after applying
    }
  }, [
    initialDepthFromUrl,
    dHViewInfo.minHeight,
    dHViewInfo.maxHeight,
    goToYPosition,
  ]);

  return (
    <div className="flex h-full">
      {isPanelVisible && (
        <DrillHolePanel
          alignValue={alignValue}
          onDepthParamChange={handleDepthParamChangeCallback} // Renamed prop
        />
      )}
      <Button
        icon={isPanelVisible ? <FaArrowLeft /> : <FaArrowRight />}
        onClick={togglePanel}
        type="text"
      ></Button>

      {previewImage && (
        <Image
          alt="preview image"
          src={previewImage}
          preview={{
            visible: !!previewImage,
            onVisibleChange: handlePreviewVisibleChange,
          }}
          style={{
            display: "none",
          }}
        />
      )}
      <div className="flex-1 relative">
        {drillHoleViewMode !== EnumDrillholeView.NoChoice && (
          <AutoSizerTmp>
            {({ width, height }) => (
              <Stage
                width={width}
                height={height}
                onMouseMove={handleMouseMove}
                ref={stageRef}
              >
                <Layer
                  offsetX={0}
                  y={150}
                  width={width}
                  height={height}
                  onWheel={handleWheel}
                  ref={layerRef}
                  draggable={!isZooming}
                  onTouchStart={handleTouchStart}
                  onTouchMove={handleTouchMove}
                  onTouchEnd={handleTouchEnd}
                >
                  <Rect
                    x={-window.innerWidth}
                    y={-window.innerHeight}
                    width={window.innerWidth * 3}
                    height={window.innerHeight * 3}
                    fill="transparent"
                  />
                  {drillHoleViewMode && (
                    <DrillHoleViewStacks
                      dHImages={imagesOfDrillHoles}
                      viewMode={drillHoleViewMode}
                      dhAttributePoints={dHAttributePoints}
                      dhSuitesLogging={dhSuitesLogging}
                      selectedHoleNames={selectedHoleNames}
                    />
                  )}
                </Layer>
                {isNumber(dHViewInfo.minHeight) &&
                  isNumber(dHViewInfo.maxHeight) && (
                    <VerticalRuler
                      height={height - 10}
                      start={dHViewInfo?.minHeight ?? 0}
                      end={dHViewInfo?.maxHeight ?? 0}
                      cursorYPosition={currentPosLayer.y}
                      goToYPosition={goToYPosition}
                      currentPosLayer={currentPosLayer}
                      setCurrentPosLayer={setCurrentPosLayer}
                    />
                  )}
              </Stage>
            )}
          </AutoSizerTmp>
        )}

        <div className="absolute bottom-0 right-0 flex gap-1 select-none">
          <div className="flex items-center">
            <Dropdown
              trigger={["click"]}
              dropdownRender={() => {
                return (
                  <div className="h-14 flex justify-center mb-2">
                    <Slider
                      value={dHViewConfigs.pointDataWidth * 10}
                      min={0}
                      max={100}
                      vertical
                      tooltip={{
                        open: false,
                      }}
                      onChange={(value) => {
                        dispatch(
                          updateDHViewConfigs({
                            pointDataWidth: value / 10,
                          }),
                        );
                      }}
                    />
                  </div>
                );
              }}
              placement="top"
            >
              <Tooltip
                title="Adjust down hole point data amplitude"
                placement="left"
              >
                <div
                  className="cursor-pointer h-10 w-12 border border-gray-300 p-0.5 flex items-center justify-center ml-1 
                    bg-white rounded-[6px]"
                >
                  <PiResizeLight />
                </div>
              </Tooltip>
            </Dropdown>

            <Tooltip
              title={
                dHViewInfo?.isHideTableInfo
                  ? "Show drillhole infomation"
                  : "Hide drillhole infomation"
              }
            >
              <div
                className={`cursor-pointer h-10 w-12 border border-gray-300 p-0.5 flex items-center justify-center ml-1 ${
                  dHViewInfo?.isHideTableInfo ? "bg-blue-300" : "bg-white"
                } rounded-[6px]`}
                onClick={() => {
                  if (dHViewInfo?.isHideTableInfo) {
                    dispatch(
                      updateDHViewInfo({
                        isHideTableInfo: false,
                      }),
                    );
                  } else {
                    dispatch(
                      updateDHViewInfo({
                        isHideTableInfo: true,
                      }),
                    );
                  }
                }}
              >
                {dHViewInfo?.isHideTableInfo ? (
                  <RiInformationLine className="text-xl" />
                ) : (
                  <RiInformationOffLine className="text-xl" />
                )}
              </div>
            </Tooltip>

            <Tooltip
              title={
                dHViewInfo?.isShowTextDepth
                  ? "Hide text depth"
                  : "Show text depth"
              }
            >
              <div
                className={`cursor-pointer h-10 w-12 border border-gray-300 p-0.5 flex items-center justify-center ml-1 ${
                  dHViewInfo?.isShowTextDepth ? "bg-white" : "bg-blue-300"
                } rounded-[6px]`}
                onClick={() => {
                  if (dHViewInfo?.isShowTextDepth) {
                    dispatch(
                      updateDHViewInfo({
                        isShowTextDepth: false,
                      }),
                    );
                  } else {
                    dispatch(
                      updateDHViewInfo({
                        isShowTextDepth: true,
                      }),
                    );
                  }
                }}
              >
                {dHViewInfo?.isShowTextDepth ? (
                  <RxTextNone className=" text-xl" />
                ) : (
                  <RxText className=" text-xl" />
                )}
              </div>
            </Tooltip>

            <Tooltip
              title={
                dHViewInfo?.isAlignHorizontalSpaceBetween
                  ? "Align horizontal space between"
                  : "Align stack horizontal"
              }
            >
              <div
                className={`cursor-pointer h-10 w-12 border border-gray-300 p-0.5 flex items-center justify-center ml-1 ${
                  dHViewInfo?.isAlignHorizontalSpaceBetween
                    ? "bg-blue-300"
                    : "bg-white"
                } rounded-[6px]`}
                onClick={() => {
                  if (dHViewInfo?.isAlignHorizontalSpaceBetween) {
                    dispatch(
                      updateDHViewInfo({
                        isAlignHorizontalSpaceBetween: false,
                      }),
                    );
                    if (drillHoleViewMode) {
                      dispatch(
                        updateDHViewConfigs(
                          defaultDrillHoleViewConfig[drillHoleViewMode],
                        ),
                      );
                    }
                  } else {
                    dispatch(
                      updateDHViewInfo({
                        isAlignHorizontalSpaceBetween: true,
                      }),
                    );
                    dispatch(
                      updateDHViewConfigs({
                        gap: 0.02,
                      }),
                    );
                  }
                }}
              >
                {dHViewInfo?.isAlignHorizontalSpaceBetween ? (
                  <LuAlignHorizontalJustifyCenter />
                ) : (
                  <LuAlignHorizontalSpaceBetween />
                )}
              </div>
            </Tooltip>
          </div>
          <Input
            ref={goToDepthRef}
            placeholder="Go to depth"
            value={goToDepth}
            onChange={(e) => {
              if (/^\d*$/.test(e.target.value)) {
                setGotoDepth(e.target.value);
              }
            }}
            style={{
              width: 104,
            }}
            onKeyUp={(e) => {
              if (e.key === "Enter") {
                if (isEmpty(goToDepth)) {
                  // If input is cleared, remove depth from URL
                  const params = new URLSearchParams(window.location.search);
                  params.delete("depth");
                  const newUrl = `${
                    window.location.pathname
                  }?${params.toString()}`;
                  window.history.replaceState({}, "", newUrl);
                  return;
                }
                const newOffsetY = Number(goToDepth);
                if (!isNaN(newOffsetY)) {
                  goToYPosition(newOffsetY); // Use the existing goToYPosition

                  // Update URL
                  const params = new URLSearchParams(window.location.search);
                  params.set("depth", goToDepth);
                  const newUrl = `${
                    window.location.pathname
                  }?${params.toString()}`;
                  window.history.replaceState({}, "", newUrl);

                  goToDepthRef.current?.blur();
                  // Do not clear setGotoDepth("") here, as it should reflect the current depth
                }
              }
            }}
            className="border bg-white border-gray-300 px-2 py-1 rounded-[6px]"
          ></Input>
          <Select
            options={ImageSizeOptions}
            onChange={(value) => {
              dispatch(updatePreviewSize(value));
            }}
            style={{
              height: 40,
            }}
            value={previewImageSize}
            placeholder="Preview Quatity"
          />

          <ButtonCommon
            className="border bg-white border-gray-300 px-2 py-1 rounded-[6px]"
            onClick={reset}
          >
            Reset
          </ButtonCommon>
          <div className="h-10 w-14 border border-gray-300 p-1 flex items-center justify-center bg-white rounded-[6px]">
            x{currentScale}
          </div>
        </div>
      </div>
    </div>
  );
}

export default memo(DrillHoleView);
