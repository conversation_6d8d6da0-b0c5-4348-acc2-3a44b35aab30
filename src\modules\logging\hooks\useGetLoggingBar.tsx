import { useState } from "react";

import dataEntryRequest, { LoggingBarItem } from "../api/data-entry.api";

export const useGetLoggingBar = () => {
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<LoggingBarItem[]>([]);

  const request = async (
    params: {
      GeologySuiteId: number;
      DrillholeId: number;
      ImageSubtypeId?: number;
      ImageTypeId?: number;
      SkipCount?: number;
      MaxResultCount?: number;
    },
    onSuccess?: Function,
    onError?: Function,
  ) => {
    setLoading(true);
    const response = await dataEntryRequest.getAllLoggingBar({
      ...params,
    });
    if (response?.state === "success") {
      setData(response.data?.items || []);
      setTotal(response.data?.totalCount || 0);
      setLoading(false);
      onSuccess?.(response.data);
      return response.data;
    } else {
      onError?.(response);
      setLoading(false);
      return null;
    }
  };

  return { request, loading, data, total };
};
