interface Props {
  requestFunc: (filter: any) => any;
  filter: object;
}
async function useGetAllRecord<T = any>({
  requestFunc = () => [],
  filter = {
    page: 1,
    pageSize: 50,
  },
}: Props) {
  let currentPage = 1;
  let endPage = 1;
  const RECORDS_PER_REQUEST = 100;
  let results = [] as T[];

  while (currentPage <= endPage) {
    const records = await requestFunc({
      ...filter,
      page: currentPage,
      pageSize: RECORDS_PER_REQUEST,
    });
    currentPage = currentPage + 1;
    endPage = Number(records?.data?.pagination?.totalPages ?? 1);
    results = [...results, ...(records?.data?.records ?? [])];
  }
  return results;
}

export default useGetAllRecord;
