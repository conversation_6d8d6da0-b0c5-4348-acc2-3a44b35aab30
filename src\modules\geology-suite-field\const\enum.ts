export enum FieldType {
  Colour = 1,
  <PERSON><PERSON><PERSON>,
  RockGroup,
  PickList,
  Description,
  RockType,
  RockSelect,
  DateField,
  RockTree,
  Checkbox,
  Latitude,
  Longitude,
  Url,
}
export const FieldTypeOptions = [
  {
    label: "Colour",
    value: FieldType.Colour,
  },
  {
    label: "Number Field",
    value: FieldType.NumberField,
  },
  {
    label: "Rock Group",
    value: FieldType.RockGroup,
  },
  {
    label: "Pick List",
    value: FieldType.PickList,
  },
  {
    label: "Description",
    value: FieldType.Description,
  },
  {
    label: "Rock Type – Number Field",
    value: FieldType.RockType,
  },
  {
    label: "Rock Select - Number Field",
    value: FieldType.RockSelect,
  },
  {
    label: "Date Field",
    value: FieldType.DateField,
  },
  {
    label: "Rock Tree",
    value: FieldType.RockTree,
  },
  {
    label: "Checkbox",
    value: FieldType.Checkbox,
  },
  {
    label: "Latitude",
    value: FieldType.Latitude,
  },
  {
    label: "Longitude",
    value: FieldType.Longitude,
  },
  {
    label: "URL",
    value: FieldType.Url,
  },
];
