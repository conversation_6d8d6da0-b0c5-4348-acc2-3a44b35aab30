import {
  DeleteOutlined,
  ReloadOutlined,
  UploadOutlined,
} from "@ant-design/icons";
import { Button, Image, message, Upload } from "antd";
import { useCallback, useMemo } from "react";
import { Control, UseFormSetValue, useWatch } from "react-hook-form";

import imageRequest from "@/modules/image/api/image.api";
import { DataEntryBody } from "../../api/data-entry.api";

interface RenderUrlFieldProps {
  control: Control<DataEntryBody>;
  index: number;
  setValue: UseFormSetValue<DataEntryBody>;
}

export default function RenderUrlField({
  control,
  index,
  setValue,
}: RenderUrlFieldProps) {
  // Use useWatch for real-time updates
  const watchedValue = useWatch({
    control,
    name: `dataEntryValues.${index}.url`,
  });

  // Memoize the current value to avoid unnecessary re-renders
  const currentValue = useMemo(() => watchedValue || "", [watchedValue]);

  const handleUpload = useCallback(
    async (file: File) => {
      // Validate file type
      if (!file.type.startsWith("image/")) {
        message.error("Only image files are allowed!");
        return false;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        message.error("File too large! Maximum 5MB.");
        return false;
      }

      try {
        // Upload to server using API like field-url.tsx
        const uploadResult = await imageRequest.uploadImage({
          image: file,
        });

        if (uploadResult.state === "success") {
          const imageUrl = uploadResult.data?.result;

          // Update form value with URL from server
          setValue(`dataEntryValues.${index}.url`, imageUrl);
          message.success("Image uploaded successfully!");
        } else {
          message.error("Upload failed: " + uploadResult.message);
        }
      } catch {
        message.error("Upload failed!");
      }

      return false; // Prevent default upload behavior
    },
    [setValue, index],
  );

  const handleDelete = useCallback(() => {
    setValue(`dataEntryValues.${index}.url`, "");
    message.success("Image deleted!");
  }, [setValue, index]);

  return (
    <div className="w-full">
      {currentValue ? (
        // Has image - display image with controls
        <div className="relative group">
          <div className="relative overflow-hidden rounded-lg border border-gray-200 shadow-sm">
            <Image
              src={currentValue}
              alt="Uploaded image"
              className="w-full h-auto max-h-64 object-cover"
              preview={{
                mask: <div className="text-white">View Image</div>,
              }}
            />

            {/* Overlay controls */}
            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
              <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex gap-2">
                <Upload
                  beforeUpload={handleUpload}
                  showUploadList={false}
                  accept="image/*"
                >
                  <Button
                    type="primary"
                    icon={<ReloadOutlined />}
                    size="small"
                    className="shadow-lg"
                  >
                    Replace
                  </Button>
                </Upload>
                <Button
                  type="primary"
                  danger
                  icon={<DeleteOutlined />}
                  size="small"
                  onClick={handleDelete}
                  className="shadow-lg"
                >
                  Delete
                </Button>
              </div>
            </div>
          </div>
        </div>
      ) : (
        // No image - display upload area
        <div className="w-full">
          <Upload
            beforeUpload={handleUpload}
            showUploadList={false}
            accept="image/*"
          >
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 hover:bg-blue-50 transition-all duration-200 cursor-pointer">
              <UploadOutlined className="text-4xl text-gray-400 mb-4" />
              <div className="text-gray-600 font-medium mb-2">
                No image uploaded
              </div>
              <div className="text-gray-400 text-sm mb-4">
                Click to upload image or drag and drop file here
              </div>
              <Button
                type="primary"
                icon={<UploadOutlined />}
                size="large"
                className="shadow-md"
              >
                Upload Image
              </Button>
              <div className="text-xs text-gray-400 mt-2">
                Supported: JPG, PNG, GIF (max 5MB)
              </div>
            </div>
          </Upload>
        </div>
      )}
    </div>
  );
}
