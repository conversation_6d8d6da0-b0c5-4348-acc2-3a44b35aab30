type ResponsiveSpan = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;

export interface ScreenConfig {
  default?: {
    col?: ResponsiveSpan;
    grid?: ResponsiveSpan;
  };
  xs?: {
    col?: ResponsiveSpan;
    grid?: ResponsiveSpan;
  };
  sm?: {
    col?: ResponsiveSpan;
    grid?: ResponsiveSpan;
  };
  md?: {
    col?: ResponsiveSpan;
    grid?: ResponsiveSpan;
  };
  lg?: {
    col?: ResponsiveSpan;
    grid?: ResponsiveSpan;
  };
  xl?: {
    col?: ResponsiveSpan;
    grid?: ResponsiveSpan;
  };
  "2xl"?: {
    col?: ResponsiveSpan;
    grid?: ResponsiveSpan;
  };
}
