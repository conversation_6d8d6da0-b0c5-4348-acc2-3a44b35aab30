name: Staging - B<PERSON> and Push Docker Image to ACR

on:
  push:
    branches:
      - staging

jobs:
  build-and-push:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1

      - name: Log in to ACR
        run: echo ${{ secrets.ACR_PASSWORD }} | docker login ${{ secrets.ACR_NAME }}.azurecr.io -u ${{ secrets.ACR_USERNAME }} --password-stdin

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: |
            ${{ secrets.ACR_NAME }}.azurecr.io/aibase-fe:latest-staging
          build-args: |
            BUILD_ENV=staging
      - name: Log out from Docker
        run: docker logout ${{ secrets.ACR_NAME }}.azurecr.io
