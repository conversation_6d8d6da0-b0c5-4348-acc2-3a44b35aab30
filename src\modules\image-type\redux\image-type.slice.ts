import { createSlice } from "@reduxjs/toolkit";

import { RequestState } from "@/common/configs/app.contants";

import { getListImageType } from "./thunks";

const initialState: ImageTypeSliceState = {
  status: RequestState.idle,
};

export const imageTypeSlice = createSlice({
  name: "imageType",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getListImageType.pending, (state) => {
        state.status = RequestState.pending;
      })
      .addCase(getListImageType.fulfilled, (state, action) => {
        state.status = RequestState.success;
        state.result = action.payload.data;
      });
  },
});

export interface ImageTypeSliceState {
  result?: any;
  status: RequestState;
}

export const {} = imageTypeSlice.actions;
