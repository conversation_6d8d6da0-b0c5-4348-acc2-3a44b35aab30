import { useState } from "react";

import geologyLongitudeRequest from "../api/geology-longitude.api";
import { GeologyLongitudeBodyType } from "../model/schema/geology-longitude.schema";

export const useCreateGeologyLongitude = () => {
  const [loading, setLoading] = useState(false);
  async function request(
    params: GeologyLongitudeBodyType,
    onSuccess?: Function,
    onError?: Function,
  ) {
    setLoading(true);
    const response = await geologyLongitudeRequest.create(params);
    if (response.state === "success") {
      onSuccess?.(response.data);
      setLoading(false);
    } else {
      onError?.(response);
      setLoading(false);
    }
  }
  return {
    request,
    loading,
  };
};
